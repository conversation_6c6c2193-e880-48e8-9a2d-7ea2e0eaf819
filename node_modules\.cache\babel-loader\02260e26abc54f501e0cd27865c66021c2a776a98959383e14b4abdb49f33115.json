{"ast": null, "code": "// Optimized Export Service - Pareto 80/20 Implementation\n// Essential export functionality: PDF, Word, and Clipboard\n\nclass ExportService {\n  constructor() {\n    this.isSupported = {\n      clipboard: 'navigator' in window && 'clipboard' in navigator,\n      download: 'document' in window && 'createElement' in document\n    };\n  }\n\n  // Check if export features are supported\n  isAvailable() {\n    return this.isSupported;\n  }\n\n  // Copy text to clipboard\n  async copyToClipboard(text, format = 'text') {\n    if (!this.isSupported.clipboard) {\n      // Fallback for older browsers\n      return this.fallbackCopyToClipboard(text);\n    }\n    try {\n      if (format === 'html') {\n        await navigator.clipboard.write([new ClipboardItem({\n          'text/html': new Blob([text], {\n            type: 'text/html'\n          }),\n          'text/plain': new Blob([this.stripHtml(text)], {\n            type: 'text/plain'\n          })\n        })]);\n      } else {\n        await navigator.clipboard.writeText(text);\n      }\n      return {\n        success: true,\n        message: 'Content copied to clipboard!'\n      };\n    } catch (error) {\n      console.error('Clipboard copy failed:', error);\n      return this.fallbackCopyToClipboard(text);\n    }\n  }\n\n  // Fallback clipboard copy for older browsers\n  fallbackCopyToClipboard(text) {\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    textArea.style.position = 'fixed';\n    textArea.style.left = '-999999px';\n    textArea.style.top = '-999999px';\n    document.body.appendChild(textArea);\n    textArea.focus();\n    textArea.select();\n    try {\n      const successful = document.execCommand('copy');\n      document.body.removeChild(textArea);\n      return successful ? {\n        success: true,\n        message: 'Content copied to clipboard!'\n      } : {\n        success: false,\n        message: 'Failed to copy to clipboard'\n      };\n    } catch (error) {\n      document.body.removeChild(textArea);\n      return {\n        success: false,\n        message: 'Clipboard not supported'\n      };\n    }\n  }\n\n  // Export as PDF (using browser's print functionality)\n  exportAsPDF(content, filename = 'knowledge-tree-article') {\n    try {\n      // Create a new window with the content\n      const printWindow = window.open('', '_blank');\n      const htmlContent = this.formatForPrint(content, filename);\n      printWindow.document.write(htmlContent);\n      printWindow.document.close();\n\n      // Wait for content to load, then trigger print\n      printWindow.onload = () => {\n        setTimeout(() => {\n          printWindow.print();\n          // Close window after printing (user can cancel)\n          printWindow.onafterprint = () => {\n            printWindow.close();\n          };\n        }, 250);\n      };\n      return {\n        success: true,\n        message: 'PDF export initiated. Use your browser\\'s print dialog to save as PDF.'\n      };\n    } catch (error) {\n      console.error('PDF export failed:', error);\n      return {\n        success: false,\n        message: 'PDF export failed'\n      };\n    }\n  }\n\n  // Export as Word document (using HTML format that Word can open)\n  exportAsWord(content, filename = 'knowledge-tree-article') {\n    try {\n      const wordContent = this.formatForWord(content);\n      const blob = new Blob([wordContent], {\n        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n      });\n      this.downloadBlob(blob, `${filename}.doc`);\n      return {\n        success: true,\n        message: 'Word document downloaded successfully!'\n      };\n    } catch (error) {\n      console.error('Word export failed:', error);\n      return {\n        success: false,\n        message: 'Word export failed'\n      };\n    }\n  }\n\n  // Download blob as file\n  downloadBlob(blob, filename) {\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename;\n    link.style.display = 'none';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n\n    // Clean up the URL object\n    setTimeout(() => URL.revokeObjectURL(url), 100);\n  }\n\n  // Format content for printing/PDF\n  formatForPrint(content, title) {\n    const cleanContent = this.formatContent(content);\n    return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"UTF-8\">\n        <title>${title}</title>\n        <style>\n          @page {\n            margin: 2cm;\n            size: A4;\n          }\n          body {\n            font-family: 'Times New Roman', serif;\n            font-size: 12pt;\n            line-height: 1.6;\n            color: #000;\n            max-width: none;\n            margin: 0;\n            padding: 0;\n          }\n          h1 {\n            font-size: 18pt;\n            font-weight: bold;\n            margin-bottom: 1em;\n            color: #000;\n            border-bottom: 2px solid #000;\n            padding-bottom: 0.5em;\n          }\n          h2 {\n            font-size: 16pt;\n            font-weight: bold;\n            margin: 1.5em 0 0.5em 0;\n            color: #000;\n          }\n          h3 {\n            font-size: 14pt;\n            font-weight: bold;\n            margin: 1em 0 0.5em 0;\n            color: #000;\n          }\n          p {\n            margin-bottom: 1em;\n            text-align: justify;\n          }\n          ul, ol {\n            margin: 1em 0;\n            padding-left: 2em;\n          }\n          li {\n            margin-bottom: 0.5em;\n          }\n          .header {\n            text-align: center;\n            margin-bottom: 2em;\n            border-bottom: 1px solid #ccc;\n            padding-bottom: 1em;\n          }\n          .footer {\n            margin-top: 2em;\n            padding-top: 1em;\n            border-top: 1px solid #ccc;\n            font-size: 10pt;\n            color: #666;\n            text-align: center;\n          }\n          @media print {\n            body { print-color-adjust: exact; }\n            .no-print { display: none; }\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"header\">\n          <h1>${title}</h1>\n          <p>Generated by Knowledge Tree Explorer</p>\n          <p>Date: ${new Date().toLocaleDateString()}</p>\n        </div>\n        <div class=\"content\">\n          ${cleanContent}\n        </div>\n        <div class=\"footer\">\n          <p>Generated by Knowledge Tree Explorer - ${window.location.origin}</p>\n        </div>\n      </body>\n      </html>\n    `;\n  }\n\n  // Format content for Word document\n  formatForWord(content) {\n    const cleanContent = this.formatContent(content);\n    return `\n      <html xmlns:o=\"urn:schemas-microsoft-com:office:office\" \n            xmlns:w=\"urn:schemas-microsoft-com:office:word\" \n            xmlns=\"http://www.w3.org/TR/REC-html40\">\n      <head>\n        <meta charset=\"utf-8\">\n        <title>Knowledge Tree Article</title>\n        <!--[if gte mso 9]>\n        <xml>\n          <w:WordDocument>\n            <w:View>Print</w:View>\n            <w:Zoom>90</w:Zoom>\n            <w:DoNotPromptForConvert/>\n            <w:DoNotShowInsertionsAndDeletions/>\n          </w:WordDocument>\n        </xml>\n        <![endif]-->\n        <style>\n          body {\n            font-family: 'Times New Roman', serif;\n            font-size: 12pt;\n            line-height: 1.6;\n            margin: 1in;\n          }\n          h1 {\n            font-size: 18pt;\n            font-weight: bold;\n            margin-bottom: 12pt;\n            color: #000080;\n          }\n          h2 {\n            font-size: 16pt;\n            font-weight: bold;\n            margin: 18pt 0 6pt 0;\n            color: #000080;\n          }\n          h3 {\n            font-size: 14pt;\n            font-weight: bold;\n            margin: 12pt 0 6pt 0;\n            color: #000080;\n          }\n          p {\n            margin-bottom: 12pt;\n            text-align: justify;\n          }\n          ul, ol {\n            margin: 12pt 0;\n            padding-left: 24pt;\n          }\n          li {\n            margin-bottom: 6pt;\n          }\n        </style>\n      </head>\n      <body>\n        ${cleanContent}\n      </body>\n      </html>\n    `;\n  }\n\n  // Format and clean content\n  formatContent(content) {\n    if (typeof content === 'object' && content.title && content.content) {\n      // Article object format\n      return `\n        <h1>${this.escapeHtml(content.title)}</h1>\n        ${content.topic ? `<p><strong>Topic:</strong> ${this.escapeHtml(content.topic)}</p>` : ''}\n        <div class=\"article-content\">\n          ${this.formatText(content.content)}\n        </div>\n      `;\n    } else if (typeof content === 'string') {\n      // Plain text format\n      return this.formatText(content);\n    } else {\n      return '<p>No content available for export.</p>';\n    }\n  }\n\n  // Format text content (convert line breaks to paragraphs)\n  formatText(text) {\n    return text.split('\\n').filter(line => line.trim()).map(line => {\n      const trimmed = line.trim();\n      if (trimmed.startsWith('# ')) {\n        return `<h1>${this.escapeHtml(trimmed.substring(2))}</h1>`;\n      } else if (trimmed.startsWith('## ')) {\n        return `<h2>${this.escapeHtml(trimmed.substring(3))}</h2>`;\n      } else if (trimmed.startsWith('### ')) {\n        return `<h3>${this.escapeHtml(trimmed.substring(4))}</h3>`;\n      } else if (trimmed.startsWith('- ') || trimmed.startsWith('* ')) {\n        return `<li>${this.escapeHtml(trimmed.substring(2))}</li>`;\n      } else {\n        return `<p>${this.escapeHtml(trimmed)}</p>`;\n      }\n    }).join('\\n').replace(/(<li>.*<\\/li>\\n?)+/g, '<ul>$&</ul>').replace(/<\\/li>\\n<li>/g, '</li><li>');\n  }\n\n  // Escape HTML characters\n  escapeHtml(text) {\n    const div = document.createElement('div');\n    div.textContent = text;\n    return div.innerHTML;\n  }\n\n  // Strip HTML tags\n  stripHtml(html) {\n    const div = document.createElement('div');\n    div.innerHTML = html;\n    return div.textContent || div.innerText || '';\n  }\n\n  // Create export controls UI\n  createExportControls(content, container) {\n    if (!container) return null;\n    const controls = document.createElement('div');\n    controls.className = 'export-controls';\n    controls.innerHTML = `\n      <div class=\"export-controls-inner\">\n        <button class=\"export-btn export-clipboard\" title=\"Copy to Clipboard\">\n          📋 Copy\n        </button>\n        <button class=\"export-btn export-pdf\" title=\"Export as PDF\">\n          📄 PDF\n        </button>\n        <button class=\"export-btn export-word\" title=\"Export as Word\">\n          📝 Word\n        </button>\n      </div>\n    `;\n\n    // Add event listeners\n    const clipboardBtn = controls.querySelector('.export-clipboard');\n    const pdfBtn = controls.querySelector('.export-pdf');\n    const wordBtn = controls.querySelector('.export-word');\n    clipboardBtn.addEventListener('click', async () => {\n      const result = await this.copyToClipboard(typeof content === 'object' ? content.content : content);\n      this.showMessage(result.message, result.success ? 'success' : 'error');\n    });\n    pdfBtn.addEventListener('click', () => {\n      const result = this.exportAsPDF(content);\n      this.showMessage(result.message, result.success ? 'success' : 'error');\n    });\n    wordBtn.addEventListener('click', () => {\n      const result = this.exportAsWord(content);\n      this.showMessage(result.message, result.success ? 'success' : 'error');\n    });\n    container.appendChild(controls);\n    return controls;\n  }\n\n  // Show message to user\n  showMessage(message, type = 'info') {\n    // Create or update message element\n    let messageEl = document.querySelector('.export-message');\n    if (!messageEl) {\n      messageEl = document.createElement('div');\n      messageEl.className = 'export-message';\n      document.body.appendChild(messageEl);\n    }\n    messageEl.textContent = message;\n    messageEl.className = `export-message ${type}`;\n    messageEl.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      padding: 12px 16px;\n      border-radius: 6px;\n      color: white;\n      font-weight: 500;\n      z-index: 10000;\n      max-width: 300px;\n      background: ${type === 'success' ? '#047857' : type === 'error' ? '#b91c1c' : '#1d4ed8'};\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n      animation: slideInFromRight 0.3s ease-out;\n    `;\n\n    // Auto-hide after 3 seconds\n    setTimeout(() => {\n      if (messageEl) {\n        messageEl.style.animation = 'slideOutToRight 0.3s ease-in';\n        setTimeout(() => {\n          if (messageEl && messageEl.parentNode) {\n            messageEl.parentNode.removeChild(messageEl);\n          }\n        }, 300);\n      }\n    }, 3000);\n  }\n}\n\n// Create singleton instance\nconst exportService = new ExportService();\nexport default exportService;", "map": {"version": 3, "names": ["ExportService", "constructor", "isSupported", "clipboard", "window", "navigator", "download", "document", "isAvailable", "copyToClipboard", "text", "format", "fallbackCopyToClipboard", "write", "ClipboardItem", "Blob", "type", "stripHtml", "writeText", "success", "message", "error", "console", "textArea", "createElement", "value", "style", "position", "left", "top", "body", "append<PERSON><PERSON><PERSON>", "focus", "select", "successful", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "exportAsPDF", "content", "filename", "printWindow", "open", "htmlContent", "formatForPrint", "close", "onload", "setTimeout", "print", "onafterprint", "exportAsWord", "wordContent", "formatForWord", "blob", "downloadBlob", "url", "URL", "createObjectURL", "link", "href", "display", "click", "revokeObjectURL", "title", "cleanContent", "formatContent", "Date", "toLocaleDateString", "location", "origin", "escapeHtml", "topic", "formatText", "split", "filter", "line", "trim", "map", "trimmed", "startsWith", "substring", "join", "replace", "div", "textContent", "innerHTML", "html", "innerText", "createExportControls", "container", "controls", "className", "clipboardBtn", "querySelector", "pdfBtn", "wordBtn", "addEventListener", "result", "showMessage", "messageEl", "cssText", "animation", "parentNode", "exportService"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/services/exportService.js"], "sourcesContent": ["// Optimized Export Service - Pareto 80/20 Implementation\n// Essential export functionality: PDF, Word, and Clipboard\n\nclass ExportService {\n  constructor() {\n    this.isSupported = {\n      clipboard: 'navigator' in window && 'clipboard' in navigator,\n      download: 'document' in window && 'createElement' in document\n    };\n  }\n\n  // Check if export features are supported\n  isAvailable() {\n    return this.isSupported;\n  }\n\n  // Copy text to clipboard\n  async copyToClipboard(text, format = 'text') {\n    if (!this.isSupported.clipboard) {\n      // Fallback for older browsers\n      return this.fallbackCopyToClipboard(text);\n    }\n\n    try {\n      if (format === 'html') {\n        await navigator.clipboard.write([\n          new ClipboardItem({\n            'text/html': new Blob([text], { type: 'text/html' }),\n            'text/plain': new Blob([this.stripHtml(text)], { type: 'text/plain' })\n          })\n        ]);\n      } else {\n        await navigator.clipboard.writeText(text);\n      }\n      return { success: true, message: 'Content copied to clipboard!' };\n    } catch (error) {\n      console.error('Clipboard copy failed:', error);\n      return this.fallbackCopyToClipboard(text);\n    }\n  }\n\n  // Fallback clipboard copy for older browsers\n  fallbackCopyToClipboard(text) {\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    textArea.style.position = 'fixed';\n    textArea.style.left = '-999999px';\n    textArea.style.top = '-999999px';\n    document.body.appendChild(textArea);\n    textArea.focus();\n    textArea.select();\n\n    try {\n      const successful = document.execCommand('copy');\n      document.body.removeChild(textArea);\n      return successful \n        ? { success: true, message: 'Content copied to clipboard!' }\n        : { success: false, message: 'Failed to copy to clipboard' };\n    } catch (error) {\n      document.body.removeChild(textArea);\n      return { success: false, message: 'Clipboard not supported' };\n    }\n  }\n\n  // Export as PDF (using browser's print functionality)\n  exportAsPDF(content, filename = 'knowledge-tree-article') {\n    try {\n      // Create a new window with the content\n      const printWindow = window.open('', '_blank');\n      const htmlContent = this.formatForPrint(content, filename);\n      \n      printWindow.document.write(htmlContent);\n      printWindow.document.close();\n      \n      // Wait for content to load, then trigger print\n      printWindow.onload = () => {\n        setTimeout(() => {\n          printWindow.print();\n          // Close window after printing (user can cancel)\n          printWindow.onafterprint = () => {\n            printWindow.close();\n          };\n        }, 250);\n      };\n\n      return { success: true, message: 'PDF export initiated. Use your browser\\'s print dialog to save as PDF.' };\n    } catch (error) {\n      console.error('PDF export failed:', error);\n      return { success: false, message: 'PDF export failed' };\n    }\n  }\n\n  // Export as Word document (using HTML format that Word can open)\n  exportAsWord(content, filename = 'knowledge-tree-article') {\n    try {\n      const wordContent = this.formatForWord(content);\n      const blob = new Blob([wordContent], { \n        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' \n      });\n      \n      this.downloadBlob(blob, `${filename}.doc`);\n      return { success: true, message: 'Word document downloaded successfully!' };\n    } catch (error) {\n      console.error('Word export failed:', error);\n      return { success: false, message: 'Word export failed' };\n    }\n  }\n\n  // Download blob as file\n  downloadBlob(blob, filename) {\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename;\n    link.style.display = 'none';\n    \n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    \n    // Clean up the URL object\n    setTimeout(() => URL.revokeObjectURL(url), 100);\n  }\n\n  // Format content for printing/PDF\n  formatForPrint(content, title) {\n    const cleanContent = this.formatContent(content);\n    \n    return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"UTF-8\">\n        <title>${title}</title>\n        <style>\n          @page {\n            margin: 2cm;\n            size: A4;\n          }\n          body {\n            font-family: 'Times New Roman', serif;\n            font-size: 12pt;\n            line-height: 1.6;\n            color: #000;\n            max-width: none;\n            margin: 0;\n            padding: 0;\n          }\n          h1 {\n            font-size: 18pt;\n            font-weight: bold;\n            margin-bottom: 1em;\n            color: #000;\n            border-bottom: 2px solid #000;\n            padding-bottom: 0.5em;\n          }\n          h2 {\n            font-size: 16pt;\n            font-weight: bold;\n            margin: 1.5em 0 0.5em 0;\n            color: #000;\n          }\n          h3 {\n            font-size: 14pt;\n            font-weight: bold;\n            margin: 1em 0 0.5em 0;\n            color: #000;\n          }\n          p {\n            margin-bottom: 1em;\n            text-align: justify;\n          }\n          ul, ol {\n            margin: 1em 0;\n            padding-left: 2em;\n          }\n          li {\n            margin-bottom: 0.5em;\n          }\n          .header {\n            text-align: center;\n            margin-bottom: 2em;\n            border-bottom: 1px solid #ccc;\n            padding-bottom: 1em;\n          }\n          .footer {\n            margin-top: 2em;\n            padding-top: 1em;\n            border-top: 1px solid #ccc;\n            font-size: 10pt;\n            color: #666;\n            text-align: center;\n          }\n          @media print {\n            body { print-color-adjust: exact; }\n            .no-print { display: none; }\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"header\">\n          <h1>${title}</h1>\n          <p>Generated by Knowledge Tree Explorer</p>\n          <p>Date: ${new Date().toLocaleDateString()}</p>\n        </div>\n        <div class=\"content\">\n          ${cleanContent}\n        </div>\n        <div class=\"footer\">\n          <p>Generated by Knowledge Tree Explorer - ${window.location.origin}</p>\n        </div>\n      </body>\n      </html>\n    `;\n  }\n\n  // Format content for Word document\n  formatForWord(content) {\n    const cleanContent = this.formatContent(content);\n    \n    return `\n      <html xmlns:o=\"urn:schemas-microsoft-com:office:office\" \n            xmlns:w=\"urn:schemas-microsoft-com:office:word\" \n            xmlns=\"http://www.w3.org/TR/REC-html40\">\n      <head>\n        <meta charset=\"utf-8\">\n        <title>Knowledge Tree Article</title>\n        <!--[if gte mso 9]>\n        <xml>\n          <w:WordDocument>\n            <w:View>Print</w:View>\n            <w:Zoom>90</w:Zoom>\n            <w:DoNotPromptForConvert/>\n            <w:DoNotShowInsertionsAndDeletions/>\n          </w:WordDocument>\n        </xml>\n        <![endif]-->\n        <style>\n          body {\n            font-family: 'Times New Roman', serif;\n            font-size: 12pt;\n            line-height: 1.6;\n            margin: 1in;\n          }\n          h1 {\n            font-size: 18pt;\n            font-weight: bold;\n            margin-bottom: 12pt;\n            color: #000080;\n          }\n          h2 {\n            font-size: 16pt;\n            font-weight: bold;\n            margin: 18pt 0 6pt 0;\n            color: #000080;\n          }\n          h3 {\n            font-size: 14pt;\n            font-weight: bold;\n            margin: 12pt 0 6pt 0;\n            color: #000080;\n          }\n          p {\n            margin-bottom: 12pt;\n            text-align: justify;\n          }\n          ul, ol {\n            margin: 12pt 0;\n            padding-left: 24pt;\n          }\n          li {\n            margin-bottom: 6pt;\n          }\n        </style>\n      </head>\n      <body>\n        ${cleanContent}\n      </body>\n      </html>\n    `;\n  }\n\n  // Format and clean content\n  formatContent(content) {\n    if (typeof content === 'object' && content.title && content.content) {\n      // Article object format\n      return `\n        <h1>${this.escapeHtml(content.title)}</h1>\n        ${content.topic ? `<p><strong>Topic:</strong> ${this.escapeHtml(content.topic)}</p>` : ''}\n        <div class=\"article-content\">\n          ${this.formatText(content.content)}\n        </div>\n      `;\n    } else if (typeof content === 'string') {\n      // Plain text format\n      return this.formatText(content);\n    } else {\n      return '<p>No content available for export.</p>';\n    }\n  }\n\n  // Format text content (convert line breaks to paragraphs)\n  formatText(text) {\n    return text\n      .split('\\n')\n      .filter(line => line.trim())\n      .map(line => {\n        const trimmed = line.trim();\n        if (trimmed.startsWith('# ')) {\n          return `<h1>${this.escapeHtml(trimmed.substring(2))}</h1>`;\n        } else if (trimmed.startsWith('## ')) {\n          return `<h2>${this.escapeHtml(trimmed.substring(3))}</h2>`;\n        } else if (trimmed.startsWith('### ')) {\n          return `<h3>${this.escapeHtml(trimmed.substring(4))}</h3>`;\n        } else if (trimmed.startsWith('- ') || trimmed.startsWith('* ')) {\n          return `<li>${this.escapeHtml(trimmed.substring(2))}</li>`;\n        } else {\n          return `<p>${this.escapeHtml(trimmed)}</p>`;\n        }\n      })\n      .join('\\n')\n      .replace(/(<li>.*<\\/li>\\n?)+/g, '<ul>$&</ul>')\n      .replace(/<\\/li>\\n<li>/g, '</li><li>');\n  }\n\n  // Escape HTML characters\n  escapeHtml(text) {\n    const div = document.createElement('div');\n    div.textContent = text;\n    return div.innerHTML;\n  }\n\n  // Strip HTML tags\n  stripHtml(html) {\n    const div = document.createElement('div');\n    div.innerHTML = html;\n    return div.textContent || div.innerText || '';\n  }\n\n  // Create export controls UI\n  createExportControls(content, container) {\n    if (!container) return null;\n\n    const controls = document.createElement('div');\n    controls.className = 'export-controls';\n    controls.innerHTML = `\n      <div class=\"export-controls-inner\">\n        <button class=\"export-btn export-clipboard\" title=\"Copy to Clipboard\">\n          📋 Copy\n        </button>\n        <button class=\"export-btn export-pdf\" title=\"Export as PDF\">\n          📄 PDF\n        </button>\n        <button class=\"export-btn export-word\" title=\"Export as Word\">\n          📝 Word\n        </button>\n      </div>\n    `;\n\n    // Add event listeners\n    const clipboardBtn = controls.querySelector('.export-clipboard');\n    const pdfBtn = controls.querySelector('.export-pdf');\n    const wordBtn = controls.querySelector('.export-word');\n\n    clipboardBtn.addEventListener('click', async () => {\n      const result = await this.copyToClipboard(\n        typeof content === 'object' ? content.content : content\n      );\n      this.showMessage(result.message, result.success ? 'success' : 'error');\n    });\n\n    pdfBtn.addEventListener('click', () => {\n      const result = this.exportAsPDF(content);\n      this.showMessage(result.message, result.success ? 'success' : 'error');\n    });\n\n    wordBtn.addEventListener('click', () => {\n      const result = this.exportAsWord(content);\n      this.showMessage(result.message, result.success ? 'success' : 'error');\n    });\n\n    container.appendChild(controls);\n    return controls;\n  }\n\n  // Show message to user\n  showMessage(message, type = 'info') {\n    // Create or update message element\n    let messageEl = document.querySelector('.export-message');\n    if (!messageEl) {\n      messageEl = document.createElement('div');\n      messageEl.className = 'export-message';\n      document.body.appendChild(messageEl);\n    }\n\n    messageEl.textContent = message;\n    messageEl.className = `export-message ${type}`;\n    messageEl.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      padding: 12px 16px;\n      border-radius: 6px;\n      color: white;\n      font-weight: 500;\n      z-index: 10000;\n      max-width: 300px;\n      background: ${type === 'success' ? '#047857' : type === 'error' ? '#b91c1c' : '#1d4ed8'};\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n      animation: slideInFromRight 0.3s ease-out;\n    `;\n\n    // Auto-hide after 3 seconds\n    setTimeout(() => {\n      if (messageEl) {\n        messageEl.style.animation = 'slideOutToRight 0.3s ease-in';\n        setTimeout(() => {\n          if (messageEl && messageEl.parentNode) {\n            messageEl.parentNode.removeChild(messageEl);\n          }\n        }, 300);\n      }\n    }, 3000);\n  }\n}\n\n// Create singleton instance\nconst exportService = new ExportService();\n\nexport default exportService;\n"], "mappings": "AAAA;AACA;;AAEA,MAAMA,aAAa,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,WAAW,GAAG;MACjBC,SAAS,EAAE,WAAW,IAAIC,MAAM,IAAI,WAAW,IAAIC,SAAS;MAC5DC,QAAQ,EAAE,UAAU,IAAIF,MAAM,IAAI,eAAe,IAAIG;IACvD,CAAC;EACH;;EAEA;EACAC,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACN,WAAW;EACzB;;EAEA;EACA,MAAMO,eAAeA,CAACC,IAAI,EAAEC,MAAM,GAAG,MAAM,EAAE;IAC3C,IAAI,CAAC,IAAI,CAACT,WAAW,CAACC,SAAS,EAAE;MAC/B;MACA,OAAO,IAAI,CAACS,uBAAuB,CAACF,IAAI,CAAC;IAC3C;IAEA,IAAI;MACF,IAAIC,MAAM,KAAK,MAAM,EAAE;QACrB,MAAMN,SAAS,CAACF,SAAS,CAACU,KAAK,CAAC,CAC9B,IAAIC,aAAa,CAAC;UAChB,WAAW,EAAE,IAAIC,IAAI,CAAC,CAACL,IAAI,CAAC,EAAE;YAAEM,IAAI,EAAE;UAAY,CAAC,CAAC;UACpD,YAAY,EAAE,IAAID,IAAI,CAAC,CAAC,IAAI,CAACE,SAAS,CAACP,IAAI,CAAC,CAAC,EAAE;YAAEM,IAAI,EAAE;UAAa,CAAC;QACvE,CAAC,CAAC,CACH,CAAC;MACJ,CAAC,MAAM;QACL,MAAMX,SAAS,CAACF,SAAS,CAACe,SAAS,CAACR,IAAI,CAAC;MAC3C;MACA,OAAO;QAAES,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAE;MAA+B,CAAC;IACnE,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO,IAAI,CAACT,uBAAuB,CAACF,IAAI,CAAC;IAC3C;EACF;;EAEA;EACAE,uBAAuBA,CAACF,IAAI,EAAE;IAC5B,MAAMa,QAAQ,GAAGhB,QAAQ,CAACiB,aAAa,CAAC,UAAU,CAAC;IACnDD,QAAQ,CAACE,KAAK,GAAGf,IAAI;IACrBa,QAAQ,CAACG,KAAK,CAACC,QAAQ,GAAG,OAAO;IACjCJ,QAAQ,CAACG,KAAK,CAACE,IAAI,GAAG,WAAW;IACjCL,QAAQ,CAACG,KAAK,CAACG,GAAG,GAAG,WAAW;IAChCtB,QAAQ,CAACuB,IAAI,CAACC,WAAW,CAACR,QAAQ,CAAC;IACnCA,QAAQ,CAACS,KAAK,CAAC,CAAC;IAChBT,QAAQ,CAACU,MAAM,CAAC,CAAC;IAEjB,IAAI;MACF,MAAMC,UAAU,GAAG3B,QAAQ,CAAC4B,WAAW,CAAC,MAAM,CAAC;MAC/C5B,QAAQ,CAACuB,IAAI,CAACM,WAAW,CAACb,QAAQ,CAAC;MACnC,OAAOW,UAAU,GACb;QAAEf,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAE;MAA+B,CAAC,GAC1D;QAAED,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAA8B,CAAC;IAChE,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdd,QAAQ,CAACuB,IAAI,CAACM,WAAW,CAACb,QAAQ,CAAC;MACnC,OAAO;QAAEJ,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAA0B,CAAC;IAC/D;EACF;;EAEA;EACAiB,WAAWA,CAACC,OAAO,EAAEC,QAAQ,GAAG,wBAAwB,EAAE;IACxD,IAAI;MACF;MACA,MAAMC,WAAW,GAAGpC,MAAM,CAACqC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;MAC7C,MAAMC,WAAW,GAAG,IAAI,CAACC,cAAc,CAACL,OAAO,EAAEC,QAAQ,CAAC;MAE1DC,WAAW,CAACjC,QAAQ,CAACM,KAAK,CAAC6B,WAAW,CAAC;MACvCF,WAAW,CAACjC,QAAQ,CAACqC,KAAK,CAAC,CAAC;;MAE5B;MACAJ,WAAW,CAACK,MAAM,GAAG,MAAM;QACzBC,UAAU,CAAC,MAAM;UACfN,WAAW,CAACO,KAAK,CAAC,CAAC;UACnB;UACAP,WAAW,CAACQ,YAAY,GAAG,MAAM;YAC/BR,WAAW,CAACI,KAAK,CAAC,CAAC;UACrB,CAAC;QACH,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MAED,OAAO;QAAEzB,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAE;MAAyE,CAAC;IAC7G,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,OAAO;QAAEF,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAoB,CAAC;IACzD;EACF;;EAEA;EACA6B,YAAYA,CAACX,OAAO,EAAEC,QAAQ,GAAG,wBAAwB,EAAE;IACzD,IAAI;MACF,MAAMW,WAAW,GAAG,IAAI,CAACC,aAAa,CAACb,OAAO,CAAC;MAC/C,MAAMc,IAAI,GAAG,IAAIrC,IAAI,CAAC,CAACmC,WAAW,CAAC,EAAE;QACnClC,IAAI,EAAE;MACR,CAAC,CAAC;MAEF,IAAI,CAACqC,YAAY,CAACD,IAAI,EAAE,GAAGb,QAAQ,MAAM,CAAC;MAC1C,OAAO;QAAEpB,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAE;MAAyC,CAAC;IAC7E,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,OAAO;QAAEF,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAqB,CAAC;IAC1D;EACF;;EAEA;EACAiC,YAAYA,CAACD,IAAI,EAAEb,QAAQ,EAAE;IAC3B,MAAMe,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;IACrC,MAAMK,IAAI,GAAGlD,QAAQ,CAACiB,aAAa,CAAC,GAAG,CAAC;IACxCiC,IAAI,CAACC,IAAI,GAAGJ,GAAG;IACfG,IAAI,CAACnD,QAAQ,GAAGiC,QAAQ;IACxBkB,IAAI,CAAC/B,KAAK,CAACiC,OAAO,GAAG,MAAM;IAE3BpD,QAAQ,CAACuB,IAAI,CAACC,WAAW,CAAC0B,IAAI,CAAC;IAC/BA,IAAI,CAACG,KAAK,CAAC,CAAC;IACZrD,QAAQ,CAACuB,IAAI,CAACM,WAAW,CAACqB,IAAI,CAAC;;IAE/B;IACAX,UAAU,CAAC,MAAMS,GAAG,CAACM,eAAe,CAACP,GAAG,CAAC,EAAE,GAAG,CAAC;EACjD;;EAEA;EACAX,cAAcA,CAACL,OAAO,EAAEwB,KAAK,EAAE;IAC7B,MAAMC,YAAY,GAAG,IAAI,CAACC,aAAa,CAAC1B,OAAO,CAAC;IAEhD,OAAO;AACX;AACA;AACA;AACA;AACA,iBAAiBwB,KAAK;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgBA,KAAK;AACrB;AACA,qBAAqB,IAAIG,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;AACpD;AACA;AACA,YAAYH,YAAY;AACxB;AACA;AACA,sDAAsD3D,MAAM,CAAC+D,QAAQ,CAACC,MAAM;AAC5E;AACA;AACA;AACA,KAAK;EACH;;EAEA;EACAjB,aAAaA,CAACb,OAAO,EAAE;IACrB,MAAMyB,YAAY,GAAG,IAAI,CAACC,aAAa,CAAC1B,OAAO,CAAC;IAEhD,OAAO;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAUyB,YAAY;AACtB;AACA;AACA,KAAK;EACH;;EAEA;EACAC,aAAaA,CAAC1B,OAAO,EAAE;IACrB,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACwB,KAAK,IAAIxB,OAAO,CAACA,OAAO,EAAE;MACnE;MACA,OAAO;AACb,cAAc,IAAI,CAAC+B,UAAU,CAAC/B,OAAO,CAACwB,KAAK,CAAC;AAC5C,UAAUxB,OAAO,CAACgC,KAAK,GAAG,8BAA8B,IAAI,CAACD,UAAU,CAAC/B,OAAO,CAACgC,KAAK,CAAC,MAAM,GAAG,EAAE;AACjG;AACA,YAAY,IAAI,CAACC,UAAU,CAACjC,OAAO,CAACA,OAAO,CAAC;AAC5C;AACA,OAAO;IACH,CAAC,MAAM,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;MACtC;MACA,OAAO,IAAI,CAACiC,UAAU,CAACjC,OAAO,CAAC;IACjC,CAAC,MAAM;MACL,OAAO,yCAAyC;IAClD;EACF;;EAEA;EACAiC,UAAUA,CAAC7D,IAAI,EAAE;IACf,OAAOA,IAAI,CACR8D,KAAK,CAAC,IAAI,CAAC,CACXC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAC3BC,GAAG,CAACF,IAAI,IAAI;MACX,MAAMG,OAAO,GAAGH,IAAI,CAACC,IAAI,CAAC,CAAC;MAC3B,IAAIE,OAAO,CAACC,UAAU,CAAC,IAAI,CAAC,EAAE;QAC5B,OAAO,OAAO,IAAI,CAACT,UAAU,CAACQ,OAAO,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO;MAC5D,CAAC,MAAM,IAAIF,OAAO,CAACC,UAAU,CAAC,KAAK,CAAC,EAAE;QACpC,OAAO,OAAO,IAAI,CAACT,UAAU,CAACQ,OAAO,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO;MAC5D,CAAC,MAAM,IAAIF,OAAO,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;QACrC,OAAO,OAAO,IAAI,CAACT,UAAU,CAACQ,OAAO,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO;MAC5D,CAAC,MAAM,IAAIF,OAAO,CAACC,UAAU,CAAC,IAAI,CAAC,IAAID,OAAO,CAACC,UAAU,CAAC,IAAI,CAAC,EAAE;QAC/D,OAAO,OAAO,IAAI,CAACT,UAAU,CAACQ,OAAO,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO;MAC5D,CAAC,MAAM;QACL,OAAO,MAAM,IAAI,CAACV,UAAU,CAACQ,OAAO,CAAC,MAAM;MAC7C;IACF,CAAC,CAAC,CACDG,IAAI,CAAC,IAAI,CAAC,CACVC,OAAO,CAAC,qBAAqB,EAAE,aAAa,CAAC,CAC7CA,OAAO,CAAC,eAAe,EAAE,WAAW,CAAC;EAC1C;;EAEA;EACAZ,UAAUA,CAAC3D,IAAI,EAAE;IACf,MAAMwE,GAAG,GAAG3E,QAAQ,CAACiB,aAAa,CAAC,KAAK,CAAC;IACzC0D,GAAG,CAACC,WAAW,GAAGzE,IAAI;IACtB,OAAOwE,GAAG,CAACE,SAAS;EACtB;;EAEA;EACAnE,SAASA,CAACoE,IAAI,EAAE;IACd,MAAMH,GAAG,GAAG3E,QAAQ,CAACiB,aAAa,CAAC,KAAK,CAAC;IACzC0D,GAAG,CAACE,SAAS,GAAGC,IAAI;IACpB,OAAOH,GAAG,CAACC,WAAW,IAAID,GAAG,CAACI,SAAS,IAAI,EAAE;EAC/C;;EAEA;EACAC,oBAAoBA,CAACjD,OAAO,EAAEkD,SAAS,EAAE;IACvC,IAAI,CAACA,SAAS,EAAE,OAAO,IAAI;IAE3B,MAAMC,QAAQ,GAAGlF,QAAQ,CAACiB,aAAa,CAAC,KAAK,CAAC;IAC9CiE,QAAQ,CAACC,SAAS,GAAG,iBAAiB;IACtCD,QAAQ,CAACL,SAAS,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;IAED;IACA,MAAMO,YAAY,GAAGF,QAAQ,CAACG,aAAa,CAAC,mBAAmB,CAAC;IAChE,MAAMC,MAAM,GAAGJ,QAAQ,CAACG,aAAa,CAAC,aAAa,CAAC;IACpD,MAAME,OAAO,GAAGL,QAAQ,CAACG,aAAa,CAAC,cAAc,CAAC;IAEtDD,YAAY,CAACI,gBAAgB,CAAC,OAAO,EAAE,YAAY;MACjD,MAAMC,MAAM,GAAG,MAAM,IAAI,CAACvF,eAAe,CACvC,OAAO6B,OAAO,KAAK,QAAQ,GAAGA,OAAO,CAACA,OAAO,GAAGA,OAClD,CAAC;MACD,IAAI,CAAC2D,WAAW,CAACD,MAAM,CAAC5E,OAAO,EAAE4E,MAAM,CAAC7E,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;IACxE,CAAC,CAAC;IAEF0E,MAAM,CAACE,gBAAgB,CAAC,OAAO,EAAE,MAAM;MACrC,MAAMC,MAAM,GAAG,IAAI,CAAC3D,WAAW,CAACC,OAAO,CAAC;MACxC,IAAI,CAAC2D,WAAW,CAACD,MAAM,CAAC5E,OAAO,EAAE4E,MAAM,CAAC7E,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;IACxE,CAAC,CAAC;IAEF2E,OAAO,CAACC,gBAAgB,CAAC,OAAO,EAAE,MAAM;MACtC,MAAMC,MAAM,GAAG,IAAI,CAAC/C,YAAY,CAACX,OAAO,CAAC;MACzC,IAAI,CAAC2D,WAAW,CAACD,MAAM,CAAC5E,OAAO,EAAE4E,MAAM,CAAC7E,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;IACxE,CAAC,CAAC;IAEFqE,SAAS,CAACzD,WAAW,CAAC0D,QAAQ,CAAC;IAC/B,OAAOA,QAAQ;EACjB;;EAEA;EACAQ,WAAWA,CAAC7E,OAAO,EAAEJ,IAAI,GAAG,MAAM,EAAE;IAClC;IACA,IAAIkF,SAAS,GAAG3F,QAAQ,CAACqF,aAAa,CAAC,iBAAiB,CAAC;IACzD,IAAI,CAACM,SAAS,EAAE;MACdA,SAAS,GAAG3F,QAAQ,CAACiB,aAAa,CAAC,KAAK,CAAC;MACzC0E,SAAS,CAACR,SAAS,GAAG,gBAAgB;MACtCnF,QAAQ,CAACuB,IAAI,CAACC,WAAW,CAACmE,SAAS,CAAC;IACtC;IAEAA,SAAS,CAACf,WAAW,GAAG/D,OAAO;IAC/B8E,SAAS,CAACR,SAAS,GAAG,kBAAkB1E,IAAI,EAAE;IAC9CkF,SAAS,CAACxE,KAAK,CAACyE,OAAO,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoBnF,IAAI,KAAK,SAAS,GAAG,SAAS,GAAGA,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;AAC7F;AACA;AACA,KAAK;;IAED;IACA8B,UAAU,CAAC,MAAM;MACf,IAAIoD,SAAS,EAAE;QACbA,SAAS,CAACxE,KAAK,CAAC0E,SAAS,GAAG,8BAA8B;QAC1DtD,UAAU,CAAC,MAAM;UACf,IAAIoD,SAAS,IAAIA,SAAS,CAACG,UAAU,EAAE;YACrCH,SAAS,CAACG,UAAU,CAACjE,WAAW,CAAC8D,SAAS,CAAC;UAC7C;QACF,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,EAAE,IAAI,CAAC;EACV;AACF;;AAEA;AACA,MAAMI,aAAa,GAAG,IAAItG,aAAa,CAAC,CAAC;AAEzC,eAAesG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}