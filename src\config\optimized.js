// Optimized Configuration - Pareto 80/20 Approach
// Focus on essential features that provide maximum value

export const OPTIMIZED_CONFIG = {
  // Core API Configuration
  api: {
    baseURL: 'https://openrouter.ai/api/v1',
    model: 'deepseek/deepseek-r1-0528:free',
    maxTokens: 3000,
    temperature: 0.7,
    timeout: 30000
  },

  // Essential Article Flags (20% that provide 80% value)
  flags: {
    // Basic Content (Most Used)
    '-a': {
      name: 'Article',
      description: 'Comprehensive article format',
      complexity: 'low',
      estimatedTime: '2-3 min',
      category: 'basic'
    },
    '-ex': {
      name: 'Examples',
      description: 'Include practical examples',
      complexity: 'low',
      estimatedTime: '+1 min',
      category: 'basic'
    },
    '-q': {
      name: 'Quiz',
      description: 'Interactive quiz questions',
      complexity: 'medium',
      estimatedTime: '+2 min',
      category: 'interactive'
    },

    // Learning & Visualization (High Impact)
    '-vis': {
      name: 'Visual',
      description: 'Include diagrams and visualizations',
      complexity: 'medium',
      estimatedTime: '+2 min',
      category: 'visual'
    },
    '-path': {
      name: 'Learning Path',
      description: 'Structured learning progression',
      complexity: 'medium',
      estimatedTime: '+3 min',
      category: 'learning'
    },

    // Professional (Business Value)
    '-case': {
      name: 'Case Study',
      description: 'Real-world case studies',
      complexity: 'high',
      estimatedTime: '+4 min',
      category: 'professional'
    },
    '-ro': {
      name: 'Romanian Market',
      description: 'Adapted for Romanian context',
      complexity: 'low',
      estimatedTime: '+1 min',
      category: 'localization'
    }
  },

  // Simplified Subscription Tiers
  subscriptions: {
    free: {
      name: 'Free Explorer',
      price: 0,
      currency: 'RON',
      limits: {
        treesPerMonth: 5,
        articlesPerMonth: 10,
        flags: ['-a', '-ex']
      },
      features: [
        'Basic knowledge trees',
        'Simple articles',
        'Community support'
      ]
    },
    premium: {
      name: 'Premium',
      price: 49,
      currency: 'RON',
      limits: {
        treesPerMonth: -1, // unlimited
        articlesPerMonth: -1,
        flags: ['-a', '-ex', '-q', '-vis', '-path', '-ro']
      },
      features: [
        'Unlimited trees & articles',
        'Advanced flags',
        'Visual content',
        'Romanian localization',
        'Priority support'
      ]
    },
    enterprise: {
      name: 'Enterprise',
      price: 149,
      currency: 'RON',
      limits: {
        treesPerMonth: -1,
        articlesPerMonth: -1,
        flags: 'all'
      },
      features: [
        'All premium features',
        'Case studies',
        'Team collaboration',
        'API access',
        'Dedicated support'
      ]
    }
  },

  // Essential UI Settings
  ui: {
    // High contrast colors for accessibility
    colors: {
      primary: '#1d4ed8',
      primaryLight: '#3b82f6',
      primaryDark: '#1e3a8a',
      success: '#047857',
      warning: '#b45309',
      error: '#b91c1c',
      gray: {
        50: '#f8fafc',
        100: '#f1f5f9',
        200: '#e2e8f0',
        500: '#475569',
        700: '#334155',
        800: '#1e293b',
        900: '#0f172a'
      }
    },

    // Simplified animations
    animations: {
      fast: '150ms ease',
      normal: '200ms ease',
      slow: '300ms ease'
    },

    // Essential breakpoints
    breakpoints: {
      mobile: '768px',
      tablet: '1024px',
      desktop: '1200px'
    }
  },

  // Core Features Toggle
  features: {
    authentication: true,
    subscriptions: true,
    gamification: false, // Disabled for simplicity
    analytics: false, // Disabled for simplicity
    offline: false, // Disabled for simplicity
    multiLanguage: false, // Focus on Romanian only
    darkMode: false, // Simplified to light mode only
    gestures: false, // Disabled for simplicity
    textToSpeech: false, // Disabled for simplicity
    export: false // Disabled for simplicity
  },

  // Performance Settings
  performance: {
    maxCachedArticles: 10, // Reduced from 50
    maxHistoryEntries: 5, // Reduced from 20
    debounceDelay: 300,
    lazyLoadImages: true,
    preloadNextArticle: false // Disabled for simplicity
  },

  // Security Settings (Simplified)
  security: {
    enableBypass: true, // For development
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
    maxLoginAttempts: 5,
    requireEmailVerification: false // Simplified
  },

  // Error Messages
  messages: {
    errors: {
      networkError: 'Network error. Please check your connection and try again.',
      apiError: 'Service temporarily unavailable. Please try again later.',
      authRequired: 'Please log in to access this feature.',
      subscriptionRequired: 'This feature requires a premium subscription.',
      invalidInput: 'Please enter a valid topic.',
      rateLimited: 'Too many requests. Please wait a moment and try again.'
    },
    success: {
      treeGenerated: 'Knowledge tree generated successfully!',
      articleGenerated: 'Article generated successfully!',
      saved: 'Content saved successfully!',
      loginSuccess: 'Welcome back!'
    }
  },

  // Development Settings
  development: {
    enableDebugMode: process.env.NODE_ENV === 'development',
    enableMockData: false,
    logLevel: 'info',
    showPerformanceMetrics: false
  }
};

// Helper functions for configuration
export const getSubscriptionFeatures = (tier) => {
  return OPTIMIZED_CONFIG.subscriptions[tier] || OPTIMIZED_CONFIG.subscriptions.free;
};

export const canUseFlag = (flag, userTier = 'free') => {
  const subscription = getSubscriptionFeatures(userTier);
  return subscription.limits.flags === 'all' || subscription.limits.flags.includes(flag);
};

export const getAvailableFlags = (userTier = 'free') => {
  const subscription = getSubscriptionFeatures(userTier);
  if (subscription.limits.flags === 'all') {
    return Object.keys(OPTIMIZED_CONFIG.flags);
  }
  return subscription.limits.flags;
};

export const isFeatureEnabled = (feature) => {
  return OPTIMIZED_CONFIG.features[feature] === true;
};

export const getApiConfig = () => {
  return {
    ...OPTIMIZED_CONFIG.api,
    apiKey: process.env.REACT_APP_OPENROUTER_API_KEY
  };
};

// Validation helpers
export const validateTopic = (topic) => {
  if (!topic || typeof topic !== 'string') {
    return { valid: false, error: 'Topic is required' };
  }
  
  const trimmed = topic.trim();
  if (trimmed.length < 2) {
    return { valid: false, error: 'Topic must be at least 2 characters long' };
  }
  
  if (trimmed.length > 100) {
    return { valid: false, error: 'Topic must be less than 100 characters' };
  }
  
  return { valid: true, topic: trimmed };
};

export const validateFlags = (flags, userTier = 'free') => {
  if (!Array.isArray(flags)) {
    return { valid: false, error: 'Flags must be an array' };
  }
  
  const availableFlags = getAvailableFlags(userTier);
  const invalidFlags = flags.filter(flag => !availableFlags.includes(flag));
  
  if (invalidFlags.length > 0) {
    return { 
      valid: false, 
      error: `Invalid flags for your subscription: ${invalidFlags.join(', ')}` 
    };
  }
  
  return { valid: true, flags };
};

export default OPTIMIZED_CONFIG;
