{"ast": null, "code": "// Optimized Text-to-Speech Service - Pareto 80/20 Implementation\n// Essential speech functionality with speed control and stop capability\n\nclass SpeechService {\n  constructor() {\n    this.isSupported = 'speechSynthesis' in window;\n    this.isPlaying = false;\n    this.isPaused = false;\n    this.currentUtterance = null;\n    this.currentText = '';\n    this.settings = {\n      rate: 1.0,\n      // Speed: 0.1 to 10\n      pitch: 1.0,\n      // Pitch: 0 to 2\n      volume: 1.0,\n      // Volume: 0 to 1\n      voice: null,\n      // Selected voice\n      lang: 'en-US' // Language\n    };\n    this.callbacks = {\n      onStart: null,\n      onEnd: null,\n      onPause: null,\n      onResume: null,\n      onError: null\n    };\n\n    // Initialize voices when available\n    if (this.isSupported) {\n      this.initVoices();\n    }\n  }\n\n  // Initialize available voices\n  initVoices() {\n    const loadVoices = () => {\n      this.voices = speechSynthesis.getVoices();\n\n      // Set default voice (prefer English)\n      if (!this.settings.voice && this.voices.length > 0) {\n        const englishVoice = this.voices.find(voice => voice.lang.startsWith('en') && voice.default) || this.voices.find(voice => voice.lang.startsWith('en')) || this.voices[0];\n        this.settings.voice = englishVoice;\n      }\n    };\n\n    // Load voices immediately if available\n    loadVoices();\n\n    // Also listen for voices changed event (some browsers load voices asynchronously)\n    speechSynthesis.addEventListener('voiceschanged', loadVoices);\n  }\n\n  // Check if speech synthesis is supported\n  isAvailable() {\n    return this.isSupported;\n  }\n\n  // Get current status\n  getStatus() {\n    return {\n      isSupported: this.isSupported,\n      isPlaying: this.isPlaying,\n      isPaused: this.isPaused,\n      canSpeak: this.isSupported && !this.isPlaying\n    };\n  }\n\n  // Set speech settings\n  configure(newSettings) {\n    this.settings = {\n      ...this.settings,\n      ...newSettings\n    };\n\n    // Validate settings\n    this.settings.rate = Math.max(0.1, Math.min(10, this.settings.rate));\n    this.settings.pitch = Math.max(0, Math.min(2, this.settings.pitch));\n    this.settings.volume = Math.max(0, Math.min(1, this.settings.volume));\n    return this.settings;\n  }\n\n  // Set callbacks\n  setCallbacks(callbacks) {\n    this.callbacks = {\n      ...this.callbacks,\n      ...callbacks\n    };\n  }\n\n  // Speak text\n  speak(text, options = {}) {\n    if (!this.isSupported) {\n      console.warn('Speech synthesis not supported');\n      return false;\n    }\n    if (!text || text.trim() === '') {\n      console.warn('No text provided for speech');\n      return false;\n    }\n\n    // Stop any current speech\n    this.stop();\n\n    // Clean and prepare text\n    const cleanText = this.cleanText(text);\n    this.currentText = cleanText;\n\n    // Create utterance\n    this.currentUtterance = new SpeechSynthesisUtterance(cleanText);\n\n    // Apply settings\n    const settings = {\n      ...this.settings,\n      ...options\n    };\n    this.currentUtterance.rate = settings.rate;\n    this.currentUtterance.pitch = settings.pitch;\n    this.currentUtterance.volume = settings.volume;\n    this.currentUtterance.lang = settings.lang;\n    if (settings.voice) {\n      this.currentUtterance.voice = settings.voice;\n    }\n\n    // Set event handlers\n    this.currentUtterance.onstart = () => {\n      this.isPlaying = true;\n      this.isPaused = false;\n      if (this.callbacks.onStart) {\n        this.callbacks.onStart();\n      }\n    };\n    this.currentUtterance.onend = () => {\n      this.isPlaying = false;\n      this.isPaused = false;\n      this.currentUtterance = null;\n      if (this.callbacks.onEnd) {\n        this.callbacks.onEnd();\n      }\n    };\n    this.currentUtterance.onpause = () => {\n      this.isPaused = true;\n      if (this.callbacks.onPause) {\n        this.callbacks.onPause();\n      }\n    };\n    this.currentUtterance.onresume = () => {\n      this.isPaused = false;\n      if (this.callbacks.onResume) {\n        this.callbacks.onResume();\n      }\n    };\n    this.currentUtterance.onerror = event => {\n      this.isPlaying = false;\n      this.isPaused = false;\n      this.currentUtterance = null;\n      console.error('Speech synthesis error:', event);\n      if (this.callbacks.onError) {\n        this.callbacks.onError(event);\n      }\n    };\n\n    // Start speaking\n    speechSynthesis.speak(this.currentUtterance);\n    return true;\n  }\n\n  // Pause speech\n  pause() {\n    if (this.isSupported && this.isPlaying && !this.isPaused) {\n      speechSynthesis.pause();\n      return true;\n    }\n    return false;\n  }\n\n  // Resume speech\n  resume() {\n    if (this.isSupported && this.isPlaying && this.isPaused) {\n      speechSynthesis.resume();\n      return true;\n    }\n    return false;\n  }\n\n  // Stop speech\n  stop() {\n    if (this.isSupported) {\n      speechSynthesis.cancel();\n      this.isPlaying = false;\n      this.isPaused = false;\n      this.currentUtterance = null;\n      return true;\n    }\n    return false;\n  }\n\n  // Toggle play/pause\n  toggle() {\n    if (this.isPlaying) {\n      if (this.isPaused) {\n        return this.resume();\n      } else {\n        return this.pause();\n      }\n    }\n    return false;\n  }\n\n  // Change speech rate (speed)\n  setRate(rate) {\n    this.settings.rate = Math.max(0.1, Math.min(10, rate));\n\n    // If currently speaking, restart with new rate\n    if (this.isPlaying && this.currentText) {\n      const wasPlaying = !this.isPaused;\n      this.stop();\n      if (wasPlaying) {\n        this.speak(this.currentText);\n      }\n    }\n    return this.settings.rate;\n  }\n\n  // Get available voices\n  getVoices() {\n    return this.voices || [];\n  }\n\n  // Set voice\n  setVoice(voice) {\n    if (voice && this.voices && this.voices.includes(voice)) {\n      this.settings.voice = voice;\n      return true;\n    }\n    return false;\n  }\n\n  // Clean text for better speech synthesis\n  cleanText(text) {\n    return text\n    // Remove HTML tags\n    .replace(/<[^>]*>/g, ' ')\n    // Replace multiple spaces with single space\n    .replace(/\\s+/g, ' ')\n    // Remove special characters that might cause issues\n    .replace(/[^\\w\\s.,!?;:()-]/g, ' ')\n    // Trim whitespace\n    .trim();\n  }\n\n  // Get speech rate presets\n  getRatePresets() {\n    return {\n      'Very Slow': 0.5,\n      'Slow': 0.75,\n      'Normal': 1.0,\n      'Fast': 1.25,\n      'Very Fast': 1.5,\n      'Ultra Fast': 2.0\n    };\n  }\n\n  // Create speech control UI\n  createControls(container) {\n    if (!container) return null;\n    const controls = document.createElement('div');\n    controls.className = 'speech-controls';\n    controls.innerHTML = `\n      <div class=\"speech-controls-inner\">\n        <button class=\"speech-btn speech-play\" title=\"Play/Pause\">\n          <span class=\"play-icon\">▶️</span>\n          <span class=\"pause-icon\" style=\"display: none;\">⏸️</span>\n        </button>\n        <button class=\"speech-btn speech-stop\" title=\"Stop\">⏹️</button>\n        <div class=\"speech-rate-control\">\n          <label>Speed:</label>\n          <input type=\"range\" class=\"speech-rate-slider\" min=\"0.5\" max=\"2\" step=\"0.1\" value=\"1\">\n          <span class=\"speech-rate-value\">1.0x</span>\n        </div>\n      </div>\n    `;\n\n    // Add event listeners\n    const playBtn = controls.querySelector('.speech-play');\n    const stopBtn = controls.querySelector('.speech-stop');\n    const rateSlider = controls.querySelector('.speech-rate-slider');\n    const rateValue = controls.querySelector('.speech-rate-value');\n    playBtn.addEventListener('click', () => {\n      if (this.isPlaying) {\n        this.toggle();\n      }\n    });\n    stopBtn.addEventListener('click', () => {\n      this.stop();\n    });\n    rateSlider.addEventListener('input', e => {\n      const rate = parseFloat(e.target.value);\n      this.setRate(rate);\n      rateValue.textContent = `${rate}x`;\n    });\n\n    // Update UI based on speech status\n    this.setCallbacks({\n      onStart: () => {\n        playBtn.querySelector('.play-icon').style.display = 'none';\n        playBtn.querySelector('.pause-icon').style.display = 'inline';\n        controls.classList.add('playing');\n      },\n      onEnd: () => {\n        playBtn.querySelector('.play-icon').style.display = 'inline';\n        playBtn.querySelector('.pause-icon').style.display = 'none';\n        controls.classList.remove('playing');\n      },\n      onPause: () => {\n        playBtn.querySelector('.play-icon').style.display = 'inline';\n        playBtn.querySelector('.pause-icon').style.display = 'none';\n      },\n      onResume: () => {\n        playBtn.querySelector('.play-icon').style.display = 'none';\n        playBtn.querySelector('.pause-icon').style.display = 'inline';\n      }\n    });\n    container.appendChild(controls);\n    return controls;\n  }\n}\n\n// Create singleton instance\nconst speechService = new SpeechService();\nexport default speechService;", "map": {"version": 3, "names": ["SpeechService", "constructor", "isSupported", "window", "isPlaying", "isPaused", "currentUtterance", "currentText", "settings", "rate", "pitch", "volume", "voice", "lang", "callbacks", "onStart", "onEnd", "onPause", "onResume", "onError", "initVoices", "loadVoices", "voices", "speechSynthesis", "getVoices", "length", "englishVoice", "find", "startsWith", "default", "addEventListener", "isAvailable", "getStatus", "canSpeak", "configure", "newSettings", "Math", "max", "min", "setCallbacks", "speak", "text", "options", "console", "warn", "trim", "stop", "cleanText", "SpeechSynthesisUtterance", "onstart", "onend", "onpause", "onresume", "onerror", "event", "error", "pause", "resume", "cancel", "toggle", "setRate", "wasPlaying", "setVoice", "includes", "replace", "getRatePresets", "createControls", "container", "controls", "document", "createElement", "className", "innerHTML", "playBtn", "querySelector", "stopBtn", "rateSlider", "rateValue", "e", "parseFloat", "target", "value", "textContent", "style", "display", "classList", "add", "remove", "append<PERSON><PERSON><PERSON>", "speechService"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/services/speechService.js"], "sourcesContent": ["// Optimized Text-to-Speech Service - Pareto 80/20 Implementation\n// Essential speech functionality with speed control and stop capability\n\nclass SpeechService {\n  constructor() {\n    this.isSupported = 'speechSynthesis' in window;\n    this.isPlaying = false;\n    this.isPaused = false;\n    this.currentUtterance = null;\n    this.currentText = '';\n    this.settings = {\n      rate: 1.0,        // Speed: 0.1 to 10\n      pitch: 1.0,       // Pitch: 0 to 2\n      volume: 1.0,      // Volume: 0 to 1\n      voice: null,      // Selected voice\n      lang: 'en-US'     // Language\n    };\n    this.callbacks = {\n      onStart: null,\n      onEnd: null,\n      onPause: null,\n      onResume: null,\n      onError: null\n    };\n\n    // Initialize voices when available\n    if (this.isSupported) {\n      this.initVoices();\n    }\n  }\n\n  // Initialize available voices\n  initVoices() {\n    const loadVoices = () => {\n      this.voices = speechSynthesis.getVoices();\n      \n      // Set default voice (prefer English)\n      if (!this.settings.voice && this.voices.length > 0) {\n        const englishVoice = this.voices.find(voice => \n          voice.lang.startsWith('en') && voice.default\n        ) || this.voices.find(voice => \n          voice.lang.startsWith('en')\n        ) || this.voices[0];\n        \n        this.settings.voice = englishVoice;\n      }\n    };\n\n    // Load voices immediately if available\n    loadVoices();\n\n    // Also listen for voices changed event (some browsers load voices asynchronously)\n    speechSynthesis.addEventListener('voiceschanged', loadVoices);\n  }\n\n  // Check if speech synthesis is supported\n  isAvailable() {\n    return this.isSupported;\n  }\n\n  // Get current status\n  getStatus() {\n    return {\n      isSupported: this.isSupported,\n      isPlaying: this.isPlaying,\n      isPaused: this.isPaused,\n      canSpeak: this.isSupported && !this.isPlaying\n    };\n  }\n\n  // Set speech settings\n  configure(newSettings) {\n    this.settings = { ...this.settings, ...newSettings };\n    \n    // Validate settings\n    this.settings.rate = Math.max(0.1, Math.min(10, this.settings.rate));\n    this.settings.pitch = Math.max(0, Math.min(2, this.settings.pitch));\n    this.settings.volume = Math.max(0, Math.min(1, this.settings.volume));\n    \n    return this.settings;\n  }\n\n  // Set callbacks\n  setCallbacks(callbacks) {\n    this.callbacks = { ...this.callbacks, ...callbacks };\n  }\n\n  // Speak text\n  speak(text, options = {}) {\n    if (!this.isSupported) {\n      console.warn('Speech synthesis not supported');\n      return false;\n    }\n\n    if (!text || text.trim() === '') {\n      console.warn('No text provided for speech');\n      return false;\n    }\n\n    // Stop any current speech\n    this.stop();\n\n    // Clean and prepare text\n    const cleanText = this.cleanText(text);\n    this.currentText = cleanText;\n\n    // Create utterance\n    this.currentUtterance = new SpeechSynthesisUtterance(cleanText);\n    \n    // Apply settings\n    const settings = { ...this.settings, ...options };\n    this.currentUtterance.rate = settings.rate;\n    this.currentUtterance.pitch = settings.pitch;\n    this.currentUtterance.volume = settings.volume;\n    this.currentUtterance.lang = settings.lang;\n    \n    if (settings.voice) {\n      this.currentUtterance.voice = settings.voice;\n    }\n\n    // Set event handlers\n    this.currentUtterance.onstart = () => {\n      this.isPlaying = true;\n      this.isPaused = false;\n      if (this.callbacks.onStart) {\n        this.callbacks.onStart();\n      }\n    };\n\n    this.currentUtterance.onend = () => {\n      this.isPlaying = false;\n      this.isPaused = false;\n      this.currentUtterance = null;\n      if (this.callbacks.onEnd) {\n        this.callbacks.onEnd();\n      }\n    };\n\n    this.currentUtterance.onpause = () => {\n      this.isPaused = true;\n      if (this.callbacks.onPause) {\n        this.callbacks.onPause();\n      }\n    };\n\n    this.currentUtterance.onresume = () => {\n      this.isPaused = false;\n      if (this.callbacks.onResume) {\n        this.callbacks.onResume();\n      }\n    };\n\n    this.currentUtterance.onerror = (event) => {\n      this.isPlaying = false;\n      this.isPaused = false;\n      this.currentUtterance = null;\n      console.error('Speech synthesis error:', event);\n      if (this.callbacks.onError) {\n        this.callbacks.onError(event);\n      }\n    };\n\n    // Start speaking\n    speechSynthesis.speak(this.currentUtterance);\n    return true;\n  }\n\n  // Pause speech\n  pause() {\n    if (this.isSupported && this.isPlaying && !this.isPaused) {\n      speechSynthesis.pause();\n      return true;\n    }\n    return false;\n  }\n\n  // Resume speech\n  resume() {\n    if (this.isSupported && this.isPlaying && this.isPaused) {\n      speechSynthesis.resume();\n      return true;\n    }\n    return false;\n  }\n\n  // Stop speech\n  stop() {\n    if (this.isSupported) {\n      speechSynthesis.cancel();\n      this.isPlaying = false;\n      this.isPaused = false;\n      this.currentUtterance = null;\n      return true;\n    }\n    return false;\n  }\n\n  // Toggle play/pause\n  toggle() {\n    if (this.isPlaying) {\n      if (this.isPaused) {\n        return this.resume();\n      } else {\n        return this.pause();\n      }\n    }\n    return false;\n  }\n\n  // Change speech rate (speed)\n  setRate(rate) {\n    this.settings.rate = Math.max(0.1, Math.min(10, rate));\n    \n    // If currently speaking, restart with new rate\n    if (this.isPlaying && this.currentText) {\n      const wasPlaying = !this.isPaused;\n      this.stop();\n      if (wasPlaying) {\n        this.speak(this.currentText);\n      }\n    }\n    \n    return this.settings.rate;\n  }\n\n  // Get available voices\n  getVoices() {\n    return this.voices || [];\n  }\n\n  // Set voice\n  setVoice(voice) {\n    if (voice && this.voices && this.voices.includes(voice)) {\n      this.settings.voice = voice;\n      return true;\n    }\n    return false;\n  }\n\n  // Clean text for better speech synthesis\n  cleanText(text) {\n    return text\n      // Remove HTML tags\n      .replace(/<[^>]*>/g, ' ')\n      // Replace multiple spaces with single space\n      .replace(/\\s+/g, ' ')\n      // Remove special characters that might cause issues\n      .replace(/[^\\w\\s.,!?;:()-]/g, ' ')\n      // Trim whitespace\n      .trim();\n  }\n\n  // Get speech rate presets\n  getRatePresets() {\n    return {\n      'Very Slow': 0.5,\n      'Slow': 0.75,\n      'Normal': 1.0,\n      'Fast': 1.25,\n      'Very Fast': 1.5,\n      'Ultra Fast': 2.0\n    };\n  }\n\n  // Create speech control UI\n  createControls(container) {\n    if (!container) return null;\n\n    const controls = document.createElement('div');\n    controls.className = 'speech-controls';\n    controls.innerHTML = `\n      <div class=\"speech-controls-inner\">\n        <button class=\"speech-btn speech-play\" title=\"Play/Pause\">\n          <span class=\"play-icon\">▶️</span>\n          <span class=\"pause-icon\" style=\"display: none;\">⏸️</span>\n        </button>\n        <button class=\"speech-btn speech-stop\" title=\"Stop\">⏹️</button>\n        <div class=\"speech-rate-control\">\n          <label>Speed:</label>\n          <input type=\"range\" class=\"speech-rate-slider\" min=\"0.5\" max=\"2\" step=\"0.1\" value=\"1\">\n          <span class=\"speech-rate-value\">1.0x</span>\n        </div>\n      </div>\n    `;\n\n    // Add event listeners\n    const playBtn = controls.querySelector('.speech-play');\n    const stopBtn = controls.querySelector('.speech-stop');\n    const rateSlider = controls.querySelector('.speech-rate-slider');\n    const rateValue = controls.querySelector('.speech-rate-value');\n\n    playBtn.addEventListener('click', () => {\n      if (this.isPlaying) {\n        this.toggle();\n      }\n    });\n\n    stopBtn.addEventListener('click', () => {\n      this.stop();\n    });\n\n    rateSlider.addEventListener('input', (e) => {\n      const rate = parseFloat(e.target.value);\n      this.setRate(rate);\n      rateValue.textContent = `${rate}x`;\n    });\n\n    // Update UI based on speech status\n    this.setCallbacks({\n      onStart: () => {\n        playBtn.querySelector('.play-icon').style.display = 'none';\n        playBtn.querySelector('.pause-icon').style.display = 'inline';\n        controls.classList.add('playing');\n      },\n      onEnd: () => {\n        playBtn.querySelector('.play-icon').style.display = 'inline';\n        playBtn.querySelector('.pause-icon').style.display = 'none';\n        controls.classList.remove('playing');\n      },\n      onPause: () => {\n        playBtn.querySelector('.play-icon').style.display = 'inline';\n        playBtn.querySelector('.pause-icon').style.display = 'none';\n      },\n      onResume: () => {\n        playBtn.querySelector('.play-icon').style.display = 'none';\n        playBtn.querySelector('.pause-icon').style.display = 'inline';\n      }\n    });\n\n    container.appendChild(controls);\n    return controls;\n  }\n}\n\n// Create singleton instance\nconst speechService = new SpeechService();\n\nexport default speechService;\n"], "mappings": "AAAA;AACA;;AAEA,MAAMA,aAAa,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,WAAW,GAAG,iBAAiB,IAAIC,MAAM;IAC9C,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,QAAQ,GAAG;MACdC,IAAI,EAAE,GAAG;MAAS;MAClBC,KAAK,EAAE,GAAG;MAAQ;MAClBC,MAAM,EAAE,GAAG;MAAO;MAClBC,KAAK,EAAE,IAAI;MAAO;MAClBC,IAAI,EAAE,OAAO,CAAK;IACpB,CAAC;IACD,IAAI,CAACC,SAAS,GAAG;MACfC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE;IACX,CAAC;;IAED;IACA,IAAI,IAAI,CAACjB,WAAW,EAAE;MACpB,IAAI,CAACkB,UAAU,CAAC,CAAC;IACnB;EACF;;EAEA;EACAA,UAAUA,CAAA,EAAG;IACX,MAAMC,UAAU,GAAGA,CAAA,KAAM;MACvB,IAAI,CAACC,MAAM,GAAGC,eAAe,CAACC,SAAS,CAAC,CAAC;;MAEzC;MACA,IAAI,CAAC,IAAI,CAAChB,QAAQ,CAACI,KAAK,IAAI,IAAI,CAACU,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE;QAClD,MAAMC,YAAY,GAAG,IAAI,CAACJ,MAAM,CAACK,IAAI,CAACf,KAAK,IACzCA,KAAK,CAACC,IAAI,CAACe,UAAU,CAAC,IAAI,CAAC,IAAIhB,KAAK,CAACiB,OACvC,CAAC,IAAI,IAAI,CAACP,MAAM,CAACK,IAAI,CAACf,KAAK,IACzBA,KAAK,CAACC,IAAI,CAACe,UAAU,CAAC,IAAI,CAC5B,CAAC,IAAI,IAAI,CAACN,MAAM,CAAC,CAAC,CAAC;QAEnB,IAAI,CAACd,QAAQ,CAACI,KAAK,GAAGc,YAAY;MACpC;IACF,CAAC;;IAED;IACAL,UAAU,CAAC,CAAC;;IAEZ;IACAE,eAAe,CAACO,gBAAgB,CAAC,eAAe,EAAET,UAAU,CAAC;EAC/D;;EAEA;EACAU,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC7B,WAAW;EACzB;;EAEA;EACA8B,SAASA,CAAA,EAAG;IACV,OAAO;MACL9B,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BE,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB4B,QAAQ,EAAE,IAAI,CAAC/B,WAAW,IAAI,CAAC,IAAI,CAACE;IACtC,CAAC;EACH;;EAEA;EACA8B,SAASA,CAACC,WAAW,EAAE;IACrB,IAAI,CAAC3B,QAAQ,GAAG;MAAE,GAAG,IAAI,CAACA,QAAQ;MAAE,GAAG2B;IAAY,CAAC;;IAEpD;IACA,IAAI,CAAC3B,QAAQ,CAACC,IAAI,GAAG2B,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC9B,QAAQ,CAACC,IAAI,CAAC,CAAC;IACpE,IAAI,CAACD,QAAQ,CAACE,KAAK,GAAG0B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC9B,QAAQ,CAACE,KAAK,CAAC,CAAC;IACnE,IAAI,CAACF,QAAQ,CAACG,MAAM,GAAGyB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC9B,QAAQ,CAACG,MAAM,CAAC,CAAC;IAErE,OAAO,IAAI,CAACH,QAAQ;EACtB;;EAEA;EACA+B,YAAYA,CAACzB,SAAS,EAAE;IACtB,IAAI,CAACA,SAAS,GAAG;MAAE,GAAG,IAAI,CAACA,SAAS;MAAE,GAAGA;IAAU,CAAC;EACtD;;EAEA;EACA0B,KAAKA,CAACC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACxB,IAAI,CAAC,IAAI,CAACxC,WAAW,EAAE;MACrByC,OAAO,CAACC,IAAI,CAAC,gCAAgC,CAAC;MAC9C,OAAO,KAAK;IACd;IAEA,IAAI,CAACH,IAAI,IAAIA,IAAI,CAACI,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC/BF,OAAO,CAACC,IAAI,CAAC,6BAA6B,CAAC;MAC3C,OAAO,KAAK;IACd;;IAEA;IACA,IAAI,CAACE,IAAI,CAAC,CAAC;;IAEX;IACA,MAAMC,SAAS,GAAG,IAAI,CAACA,SAAS,CAACN,IAAI,CAAC;IACtC,IAAI,CAAClC,WAAW,GAAGwC,SAAS;;IAE5B;IACA,IAAI,CAACzC,gBAAgB,GAAG,IAAI0C,wBAAwB,CAACD,SAAS,CAAC;;IAE/D;IACA,MAAMvC,QAAQ,GAAG;MAAE,GAAG,IAAI,CAACA,QAAQ;MAAE,GAAGkC;IAAQ,CAAC;IACjD,IAAI,CAACpC,gBAAgB,CAACG,IAAI,GAAGD,QAAQ,CAACC,IAAI;IAC1C,IAAI,CAACH,gBAAgB,CAACI,KAAK,GAAGF,QAAQ,CAACE,KAAK;IAC5C,IAAI,CAACJ,gBAAgB,CAACK,MAAM,GAAGH,QAAQ,CAACG,MAAM;IAC9C,IAAI,CAACL,gBAAgB,CAACO,IAAI,GAAGL,QAAQ,CAACK,IAAI;IAE1C,IAAIL,QAAQ,CAACI,KAAK,EAAE;MAClB,IAAI,CAACN,gBAAgB,CAACM,KAAK,GAAGJ,QAAQ,CAACI,KAAK;IAC9C;;IAEA;IACA,IAAI,CAACN,gBAAgB,CAAC2C,OAAO,GAAG,MAAM;MACpC,IAAI,CAAC7C,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;MACrB,IAAI,IAAI,CAACS,SAAS,CAACC,OAAO,EAAE;QAC1B,IAAI,CAACD,SAAS,CAACC,OAAO,CAAC,CAAC;MAC1B;IACF,CAAC;IAED,IAAI,CAACT,gBAAgB,CAAC4C,KAAK,GAAG,MAAM;MAClC,IAAI,CAAC9C,SAAS,GAAG,KAAK;MACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,IAAI,IAAI,CAACQ,SAAS,CAACE,KAAK,EAAE;QACxB,IAAI,CAACF,SAAS,CAACE,KAAK,CAAC,CAAC;MACxB;IACF,CAAC;IAED,IAAI,CAACV,gBAAgB,CAAC6C,OAAO,GAAG,MAAM;MACpC,IAAI,CAAC9C,QAAQ,GAAG,IAAI;MACpB,IAAI,IAAI,CAACS,SAAS,CAACG,OAAO,EAAE;QAC1B,IAAI,CAACH,SAAS,CAACG,OAAO,CAAC,CAAC;MAC1B;IACF,CAAC;IAED,IAAI,CAACX,gBAAgB,CAAC8C,QAAQ,GAAG,MAAM;MACrC,IAAI,CAAC/C,QAAQ,GAAG,KAAK;MACrB,IAAI,IAAI,CAACS,SAAS,CAACI,QAAQ,EAAE;QAC3B,IAAI,CAACJ,SAAS,CAACI,QAAQ,CAAC,CAAC;MAC3B;IACF,CAAC;IAED,IAAI,CAACZ,gBAAgB,CAAC+C,OAAO,GAAIC,KAAK,IAAK;MACzC,IAAI,CAAClD,SAAS,GAAG,KAAK;MACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5BqC,OAAO,CAACY,KAAK,CAAC,yBAAyB,EAAED,KAAK,CAAC;MAC/C,IAAI,IAAI,CAACxC,SAAS,CAACK,OAAO,EAAE;QAC1B,IAAI,CAACL,SAAS,CAACK,OAAO,CAACmC,KAAK,CAAC;MAC/B;IACF,CAAC;;IAED;IACA/B,eAAe,CAACiB,KAAK,CAAC,IAAI,CAAClC,gBAAgB,CAAC;IAC5C,OAAO,IAAI;EACb;;EAEA;EACAkD,KAAKA,CAAA,EAAG;IACN,IAAI,IAAI,CAACtD,WAAW,IAAI,IAAI,CAACE,SAAS,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MACxDkB,eAAe,CAACiC,KAAK,CAAC,CAAC;MACvB,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;;EAEA;EACAC,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACvD,WAAW,IAAI,IAAI,CAACE,SAAS,IAAI,IAAI,CAACC,QAAQ,EAAE;MACvDkB,eAAe,CAACkC,MAAM,CAAC,CAAC;MACxB,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;;EAEA;EACAX,IAAIA,CAAA,EAAG;IACL,IAAI,IAAI,CAAC5C,WAAW,EAAE;MACpBqB,eAAe,CAACmC,MAAM,CAAC,CAAC;MACxB,IAAI,CAACtD,SAAS,GAAG,KAAK;MACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;;EAEA;EACAqD,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACvD,SAAS,EAAE;MAClB,IAAI,IAAI,CAACC,QAAQ,EAAE;QACjB,OAAO,IAAI,CAACoD,MAAM,CAAC,CAAC;MACtB,CAAC,MAAM;QACL,OAAO,IAAI,CAACD,KAAK,CAAC,CAAC;MACrB;IACF;IACA,OAAO,KAAK;EACd;;EAEA;EACAI,OAAOA,CAACnD,IAAI,EAAE;IACZ,IAAI,CAACD,QAAQ,CAACC,IAAI,GAAG2B,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE7B,IAAI,CAAC,CAAC;;IAEtD;IACA,IAAI,IAAI,CAACL,SAAS,IAAI,IAAI,CAACG,WAAW,EAAE;MACtC,MAAMsD,UAAU,GAAG,CAAC,IAAI,CAACxD,QAAQ;MACjC,IAAI,CAACyC,IAAI,CAAC,CAAC;MACX,IAAIe,UAAU,EAAE;QACd,IAAI,CAACrB,KAAK,CAAC,IAAI,CAACjC,WAAW,CAAC;MAC9B;IACF;IAEA,OAAO,IAAI,CAACC,QAAQ,CAACC,IAAI;EAC3B;;EAEA;EACAe,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAACF,MAAM,IAAI,EAAE;EAC1B;;EAEA;EACAwC,QAAQA,CAAClD,KAAK,EAAE;IACd,IAAIA,KAAK,IAAI,IAAI,CAACU,MAAM,IAAI,IAAI,CAACA,MAAM,CAACyC,QAAQ,CAACnD,KAAK,CAAC,EAAE;MACvD,IAAI,CAACJ,QAAQ,CAACI,KAAK,GAAGA,KAAK;MAC3B,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;;EAEA;EACAmC,SAASA,CAACN,IAAI,EAAE;IACd,OAAOA;IACL;IAAA,CACCuB,OAAO,CAAC,UAAU,EAAE,GAAG;IACxB;IAAA,CACCA,OAAO,CAAC,MAAM,EAAE,GAAG;IACpB;IAAA,CACCA,OAAO,CAAC,mBAAmB,EAAE,GAAG;IACjC;IAAA,CACCnB,IAAI,CAAC,CAAC;EACX;;EAEA;EACAoB,cAAcA,CAAA,EAAG;IACf,OAAO;MACL,WAAW,EAAE,GAAG;MAChB,MAAM,EAAE,IAAI;MACZ,QAAQ,EAAE,GAAG;MACb,MAAM,EAAE,IAAI;MACZ,WAAW,EAAE,GAAG;MAChB,YAAY,EAAE;IAChB,CAAC;EACH;;EAEA;EACAC,cAAcA,CAACC,SAAS,EAAE;IACxB,IAAI,CAACA,SAAS,EAAE,OAAO,IAAI;IAE3B,MAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC9CF,QAAQ,CAACG,SAAS,GAAG,iBAAiB;IACtCH,QAAQ,CAACI,SAAS,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;IAED;IACA,MAAMC,OAAO,GAAGL,QAAQ,CAACM,aAAa,CAAC,cAAc,CAAC;IACtD,MAAMC,OAAO,GAAGP,QAAQ,CAACM,aAAa,CAAC,cAAc,CAAC;IACtD,MAAME,UAAU,GAAGR,QAAQ,CAACM,aAAa,CAAC,qBAAqB,CAAC;IAChE,MAAMG,SAAS,GAAGT,QAAQ,CAACM,aAAa,CAAC,oBAAoB,CAAC;IAE9DD,OAAO,CAAC3C,gBAAgB,CAAC,OAAO,EAAE,MAAM;MACtC,IAAI,IAAI,CAAC1B,SAAS,EAAE;QAClB,IAAI,CAACuD,MAAM,CAAC,CAAC;MACf;IACF,CAAC,CAAC;IAEFgB,OAAO,CAAC7C,gBAAgB,CAAC,OAAO,EAAE,MAAM;MACtC,IAAI,CAACgB,IAAI,CAAC,CAAC;IACb,CAAC,CAAC;IAEF8B,UAAU,CAAC9C,gBAAgB,CAAC,OAAO,EAAGgD,CAAC,IAAK;MAC1C,MAAMrE,IAAI,GAAGsE,UAAU,CAACD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC;MACvC,IAAI,CAACrB,OAAO,CAACnD,IAAI,CAAC;MAClBoE,SAAS,CAACK,WAAW,GAAG,GAAGzE,IAAI,GAAG;IACpC,CAAC,CAAC;;IAEF;IACA,IAAI,CAAC8B,YAAY,CAAC;MAChBxB,OAAO,EAAEA,CAAA,KAAM;QACb0D,OAAO,CAACC,aAAa,CAAC,YAAY,CAAC,CAACS,KAAK,CAACC,OAAO,GAAG,MAAM;QAC1DX,OAAO,CAACC,aAAa,CAAC,aAAa,CAAC,CAACS,KAAK,CAACC,OAAO,GAAG,QAAQ;QAC7DhB,QAAQ,CAACiB,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;MACnC,CAAC;MACDtE,KAAK,EAAEA,CAAA,KAAM;QACXyD,OAAO,CAACC,aAAa,CAAC,YAAY,CAAC,CAACS,KAAK,CAACC,OAAO,GAAG,QAAQ;QAC5DX,OAAO,CAACC,aAAa,CAAC,aAAa,CAAC,CAACS,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3DhB,QAAQ,CAACiB,SAAS,CAACE,MAAM,CAAC,SAAS,CAAC;MACtC,CAAC;MACDtE,OAAO,EAAEA,CAAA,KAAM;QACbwD,OAAO,CAACC,aAAa,CAAC,YAAY,CAAC,CAACS,KAAK,CAACC,OAAO,GAAG,QAAQ;QAC5DX,OAAO,CAACC,aAAa,CAAC,aAAa,CAAC,CAACS,KAAK,CAACC,OAAO,GAAG,MAAM;MAC7D,CAAC;MACDlE,QAAQ,EAAEA,CAAA,KAAM;QACduD,OAAO,CAACC,aAAa,CAAC,YAAY,CAAC,CAACS,KAAK,CAACC,OAAO,GAAG,MAAM;QAC1DX,OAAO,CAACC,aAAa,CAAC,aAAa,CAAC,CAACS,KAAK,CAACC,OAAO,GAAG,QAAQ;MAC/D;IACF,CAAC,CAAC;IAEFjB,SAAS,CAACqB,WAAW,CAACpB,QAAQ,CAAC;IAC/B,OAAOA,QAAQ;EACjB;AACF;;AAEA;AACA,MAAMqB,aAAa,GAAG,IAAIzF,aAAa,CAAC,CAAC;AAEzC,eAAeyF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}