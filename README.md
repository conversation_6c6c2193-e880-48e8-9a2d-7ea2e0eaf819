# 🌳 Knowledge Tree Explorer - Optimized Edition

A **streamlined, high-performance** interactive web application for exploring knowledge, optimized using the **Pareto 80/20 principle** for maximum impact with minimal complexity.

## ✨ **OPTIMIZED: Pareto 80/20 Design System**

🎯 **Focused on essential features that provide 80% of the value:**
- **High-contrast WCAG AAA compliant** colors for maximum accessibility
- **Simplified responsive design** optimized for all devices
- **Clean, professional authentication** with clear user flows
- **Streamlined navigation** with intuitive controls
- **Performance-first approach** with minimal dependencies
- **Essential features only** - removed complexity that provided little value
- **Optimized loading states** and error handling
- **Professional typography** with Inter font family

## 🚨 URGENT: Button Issues Fix

**If buttons are not working, follow these steps immediately:**

### 🚀 Method 1: Developer Panel (Easiest)
1. **Look for the 🚀 rocket button** in the top-right corner
2. **Click the 🚀 button** or **press F1 key** to open Developer Panel
3. **Click "Login as Developer"** for full premium access
4. **OR click "Bypass Security"** for quick testing

### 🔧 Method 2: Manual Fix (If Developer Panel not visible)
1. **Press F12** to open browser console
2. **Copy and paste this code**:
```javascript
localStorage.setItem('user', JSON.stringify({
  id: 'dev-001', firstName: 'Developer', subscriptionTier: 'premium',
  subscriptionLimits: { treesPerMonth: -1, articlesPerMonth: -1 }
}));
localStorage.setItem('authToken', 'dev-token-' + Date.now());
localStorage.setItem('bypassSecurity', 'true');
window.location.reload();
```
3. **Press Enter** and the page will reload with working buttons

### 🆘 Method 3: Quick Bypass
- Press **F12** → Console → Type: `localStorage.setItem('bypassSecurity', 'true'); location.reload()`

**After any method above, all buttons should work perfectly!**

---

## 🎯 **Optimized Features (Pareto 80/20)**

### 🚀 **Core Functionality (80% Impact)**
- **Automatic Knowledge Tree Generation**: Input any topic and get a comprehensive knowledge tree
- **Simplified Navigation**: Clean, intuitive interface without complex gestures
- **Dynamic Article Generation**: On-demand content creation with essential flags
- **Quick Authentication**: Streamlined login process with bypass for development
- **High-Performance Design**: Optimized for speed and accessibility

### 🎨 **Streamlined Design System**
- **High-contrast colors** ensuring WCAG AAA compliance
- **Simplified responsive design** that works perfectly on all devices
- **Professional authentication** with clear, friendly interfaces
- **Essential animations only** - removed complex micro-interactions
- **Clean typography** using Inter font family with proper hierarchy
- **Optimized loading states** and error handling

### 🎮 **Simplified Controls**
- **Click/Tap**: Select branches and navigate
- **Back Button**: Navigate to previous level
- **Quick Login**: Development bypass for testing

### 🏷️ **Essential Article Flags (Optimized)**

#### 📝 **Core Content Flags (Most Used - 80% Value)**
- `-a`: Standard comprehensive article
- `-ex`: Include practical examples
- `-q`: Interactive quiz with questions

#### 🎓 Learning & Visualization
- `-path`: Creează parcursuri de învățare personalizate
- `-vis`: Generează infografice, diagrame și vizualizări interactive
- `-vid`: Sugerează videoclipuri relevante și creează script-uri video
- `-mind`: Prezintă informația ca mind map interactiv
- `-flow`: Creează diagrame de flux și procese

#### 🏭 Industry Specific
- `-case`: Studii de caz reale cu rezultate măsurabile
- `-scenario`: Scenarii practice și simulări
- `-lab`: Experimente și teste practice
- `-mentor`: Include sfaturi de la experți și mentori
- `-mistakes`: Analiza greșelilor comune și cum să le eviți

#### 🛠️ Interactive Tools
- `-calc`: Calculator și tool-uri interactive pentru calcule
- `-template`: Template-uri și checklist-uri acționabile
- `-workshop`: Format de workshop cu exerciții practice
- `-game`: Gamificare cu puncte, achievement-uri și competiții
- `-team`: Conținut optimizat pentru echipe și colaborare

#### 📤 Sharing & Presentation
- `-share`: Format pentru partajare ușoară cu colegii
- `-present`: Generează prezentări PowerPoint-style
- `-meeting`: Agenda și puncte de discuție pentru întâlniri
- `-offline`: Conținut disponibil offline

#### 📊 Analytics & Benchmarking
- `-kpi`: Include KPI-uri relevante și metrici de măsurat
- `-benchmark`: Comparații cu best practices din industrie
- `-timeline`: Planificare temporală și milestone-uri

#### 🌍 Localization
- `-ro`: Adaptat pentru piața și legislația românească
- `-eu`: Focalizat pe regulamentele și practicile UE
- `-local`: Include exemple și practici locale

#### ⚡ Advanced Features
- `-auto`: Automatizarea proceselor prezentate
- `-predict`: Predicții și tendințe bazate pe date
- `-optimize`: Sugestii de optimizare continuă

## � Authentication & Subscription System

### 👤 **User Authentication**
- **Email/Password Login** with secure JWT tokens
- **Social Authentication** (Google, Apple, Microsoft)
- **Password Reset** and account recovery
- **User Profile Management** with editable information
- **Session Persistence** across browser sessions

### 💳 **Subscription Plans**

#### 🌱 **Free Explorer** (0 RON/month)
- 5 knowledge trees per month
- 10 articles per month
- Basic flags only (`-a`, `-t`, `-ex`)
- 10MB storage
- Community support

#### 💼 **Professional** (49 RON/month)
- **Unlimited** trees and articles
- **21 advanced flags** (all basic + learning + industry + tools + sharing + localization)
- **Team collaboration** (up to 5 users)
- **Priority support** and analytics
- **1GB storage** with PDF/DOCX export
- **Romanian market specialization**

#### 🏢 **Enterprise** (149 RON/month)
- **All 39 flags** available
- **Unlimited team members**
- **API access** and custom integrations
- **Dedicated support manager**
- **10GB storage** with white-label options
- **EU compliance tools** and automation features

### 💰 **Payment Methods**
- **Credit/Debit Cards** (Visa, Mastercard, American Express)
- **Revolut** (Business & Personal)
- **PayPal** account payments
- **Apple Pay** (Touch ID/Face ID)
- **Bank Transfer** (SEPA) - Free, 1-3 business days
- **Secure Processing** with PCI DSS compliance

### 🎯 **Permission System**
- **Usage-based limits** enforced in real-time
- **Feature access control** based on subscription tier
- **Automatic upgrades** and downgrades
- **Usage analytics** and limit tracking

## �🚀 Quick Start

### Prerequisites
- Node.js 16+ and npm
- OpenRouter API key

### Installation

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd knowledge-tree-explorer
   npm install
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and add your OpenRouter API key:
   ```env
   REACT_APP_OPENROUTER_API_KEY=your-api-key-here
   REACT_APP_SITE_URL=http://localhost:3000
   REACT_APP_SITE_NAME=Knowledge Tree Explorer
   ```

3. **Start development server**:
   ```bash
   npm start
   ```

4. **Open browser**: Navigate to `http://localhost:3000`

## 🔧 OpenRouter API Integration

The app uses OpenRouter's API with the DeepSeek R1 model for content generation:

```javascript
// Example API configuration
const client = new OpenRouterClient({
  baseURL: "https://openrouter.ai/api/v1",
  apiKey: process.env.REACT_APP_OPENROUTER_API_KEY,
  model: "deepseek/deepseek-r1-0528:free"
});
```

### API Features
- **Knowledge Tree Generation**: Creates structured learning paths
- **Dynamic Article Creation**: Generates content based on flags
- **Error Handling**: Graceful fallbacks for API failures
- **Rate Limiting**: Built-in request management

## 📱 Usage Guide

### 1. Topic Input
- Enter any topic (e.g., "Quantum Physics", "Machine Learning")
- Click "Explore Knowledge 🚀" or press Enter
- Wait for the knowledge tree to generate

### 2. Tree Navigation
- Browse branches by scrolling down
- Tap on a branch to select it
- Use gesture controls or buttons to generate articles

### 3. Article Customization
- Select multiple flags for enhanced content
- Flags are stackable (e.g., `-a -ex -q` for article + examples + quiz)
- Preview selected flags before generation

### 4. Gesture Navigation
- **Mobile**: Use touch gestures naturally
- **Desktop**: Use keyboard shortcuts (Arrow keys, V, S)
- **Debug Mode**: Press F1 to toggle gesture debugging

## 🎨 Architecture

### Component Structure
```
src/
├── components/
│   ├── TreeView.jsx          # Knowledge tree display
│   ├── ArticleView.jsx       # Article reader with TTS
│   ├── GestureHandler.jsx    # Gesture recognition
│   └── *.css                 # Component styles
├── services/
│   ├── openRouterService.js  # API integration
│   └── storageService.js     # Local storage management
└── App.jsx                   # Main application
```

### Key Technologies
- **React 18**: Modern React with hooks
- **OpenRouter API**: AI content generation
- **Web Speech API**: Text-to-speech functionality
- **LocalStorage**: Offline article storage
- **CSS3**: Modern styling with animations

## 🔧 Configuration

### Environment Variables
```env
# Required
REACT_APP_OPENROUTER_API_KEY=your-key

# Optional
REACT_APP_SITE_URL=your-site-url
REACT_APP_SITE_NAME=your-app-name
REACT_APP_DEBUG_MODE=false
```

### Customization Options
- **Theme**: Modify CSS variables for custom colors
- **Gestures**: Adjust sensitivity in GestureHandler
- **Storage**: Configure limits in storageService
- **API Model**: Change model in openRouterService

## 📊 Performance

### Optimization Features
- **Lazy Loading**: Components load on demand
- **Storage Limits**: Automatic cleanup of old data
- **Request Caching**: Reduces API calls
- **Responsive Design**: Mobile-first approach

### Storage Management
- **Articles**: Max 50 saved articles
- **History**: Max 20 navigation entries
- **Topics**: Max 10 recent topics
- **Auto-cleanup**: Removes oldest entries automatically

## 🧪 Testing

### Development Testing
```bash
# Run tests
npm test

# Lint code
npm run lint

# Format code
npm run format
```

### Manual Testing
1. **Gesture Recognition**: Use F1 debug mode
2. **API Integration**: Test with various topics
3. **Storage**: Check browser DevTools > Application > LocalStorage
4. **Responsive**: Test on different screen sizes

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Deploy Options
- **Netlify**: Drag and drop `build` folder
- **Vercel**: Connect GitHub repository
- **GitHub Pages**: Use `gh-pages` package
- **Custom Server**: Serve `build` folder

### Environment Setup
- Set production environment variables
- Configure CORS if using custom backend
- Enable HTTPS for production

## 🤝 Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenRouter**: AI API platform
- **DeepSeek**: R1 language model
- **React Team**: Amazing framework
- **Community**: Open source contributors

## 📞 Support

- **Issues**: GitHub Issues tab
- **Documentation**: This README
- **Community**: Discussions tab

---

**Built with ❤️ for interactive learning and knowledge exploration**
