// Optimized Export Service - Pareto 80/20 Implementation
// Essential export functionality: PDF, Word, and Clipboard

class ExportService {
  constructor() {
    this.isSupported = {
      clipboard: 'navigator' in window && 'clipboard' in navigator,
      download: 'document' in window && 'createElement' in document
    };
  }

  // Check if export features are supported
  isAvailable() {
    return this.isSupported;
  }

  // Copy text to clipboard
  async copyToClipboard(text, format = 'text') {
    if (!this.isSupported.clipboard) {
      // Fallback for older browsers
      return this.fallbackCopyToClipboard(text);
    }

    try {
      if (format === 'html') {
        await navigator.clipboard.write([
          new ClipboardItem({
            'text/html': new Blob([text], { type: 'text/html' }),
            'text/plain': new Blob([this.stripHtml(text)], { type: 'text/plain' })
          })
        ]);
      } else {
        await navigator.clipboard.writeText(text);
      }
      return { success: true, message: 'Content copied to clipboard!' };
    } catch (error) {
      console.error('Clipboard copy failed:', error);
      return this.fallbackCopyToClipboard(text);
    }
  }

  // Fallback clipboard copy for older browsers
  fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);
      return successful 
        ? { success: true, message: 'Content copied to clipboard!' }
        : { success: false, message: 'Failed to copy to clipboard' };
    } catch (error) {
      document.body.removeChild(textArea);
      return { success: false, message: 'Clipboard not supported' };
    }
  }

  // Export as PDF (using browser's print functionality)
  exportAsPDF(content, filename = 'knowledge-tree-article') {
    try {
      // Create a new window with the content
      const printWindow = window.open('', '_blank');
      const htmlContent = this.formatForPrint(content, filename);
      
      printWindow.document.write(htmlContent);
      printWindow.document.close();
      
      // Wait for content to load, then trigger print
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          // Close window after printing (user can cancel)
          printWindow.onafterprint = () => {
            printWindow.close();
          };
        }, 250);
      };

      return { success: true, message: 'PDF export initiated. Use your browser\'s print dialog to save as PDF.' };
    } catch (error) {
      console.error('PDF export failed:', error);
      return { success: false, message: 'PDF export failed' };
    }
  }

  // Export as Word document (using HTML format that Word can open)
  exportAsWord(content, filename = 'knowledge-tree-article') {
    try {
      const wordContent = this.formatForWord(content);
      const blob = new Blob([wordContent], { 
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
      });
      
      this.downloadBlob(blob, `${filename}.doc`);
      return { success: true, message: 'Word document downloaded successfully!' };
    } catch (error) {
      console.error('Word export failed:', error);
      return { success: false, message: 'Word export failed' };
    }
  }

  // Download blob as file
  downloadBlob(blob, filename) {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up the URL object
    setTimeout(() => URL.revokeObjectURL(url), 100);
  }

  // Format content for printing/PDF
  formatForPrint(content, title) {
    const cleanContent = this.formatContent(content);
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${title}</title>
        <style>
          @page {
            margin: 2cm;
            size: A4;
          }
          body {
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 1.6;
            color: #000;
            max-width: none;
            margin: 0;
            padding: 0;
          }
          h1 {
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 1em;
            color: #000;
            border-bottom: 2px solid #000;
            padding-bottom: 0.5em;
          }
          h2 {
            font-size: 16pt;
            font-weight: bold;
            margin: 1.5em 0 0.5em 0;
            color: #000;
          }
          h3 {
            font-size: 14pt;
            font-weight: bold;
            margin: 1em 0 0.5em 0;
            color: #000;
          }
          p {
            margin-bottom: 1em;
            text-align: justify;
          }
          ul, ol {
            margin: 1em 0;
            padding-left: 2em;
          }
          li {
            margin-bottom: 0.5em;
          }
          .header {
            text-align: center;
            margin-bottom: 2em;
            border-bottom: 1px solid #ccc;
            padding-bottom: 1em;
          }
          .footer {
            margin-top: 2em;
            padding-top: 1em;
            border-top: 1px solid #ccc;
            font-size: 10pt;
            color: #666;
            text-align: center;
          }
          @media print {
            body { print-color-adjust: exact; }
            .no-print { display: none; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${title}</h1>
          <p>Generated by Knowledge Tree Explorer</p>
          <p>Date: ${new Date().toLocaleDateString()}</p>
        </div>
        <div class="content">
          ${cleanContent}
        </div>
        <div class="footer">
          <p>Generated by Knowledge Tree Explorer - ${window.location.origin}</p>
        </div>
      </body>
      </html>
    `;
  }

  // Format content for Word document
  formatForWord(content) {
    const cleanContent = this.formatContent(content);
    
    return `
      <html xmlns:o="urn:schemas-microsoft-com:office:office" 
            xmlns:w="urn:schemas-microsoft-com:office:word" 
            xmlns="http://www.w3.org/TR/REC-html40">
      <head>
        <meta charset="utf-8">
        <title>Knowledge Tree Article</title>
        <!--[if gte mso 9]>
        <xml>
          <w:WordDocument>
            <w:View>Print</w:View>
            <w:Zoom>90</w:Zoom>
            <w:DoNotPromptForConvert/>
            <w:DoNotShowInsertionsAndDeletions/>
          </w:WordDocument>
        </xml>
        <![endif]-->
        <style>
          body {
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 1.6;
            margin: 1in;
          }
          h1 {
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 12pt;
            color: #000080;
          }
          h2 {
            font-size: 16pt;
            font-weight: bold;
            margin: 18pt 0 6pt 0;
            color: #000080;
          }
          h3 {
            font-size: 14pt;
            font-weight: bold;
            margin: 12pt 0 6pt 0;
            color: #000080;
          }
          p {
            margin-bottom: 12pt;
            text-align: justify;
          }
          ul, ol {
            margin: 12pt 0;
            padding-left: 24pt;
          }
          li {
            margin-bottom: 6pt;
          }
        </style>
      </head>
      <body>
        ${cleanContent}
      </body>
      </html>
    `;
  }

  // Format and clean content
  formatContent(content) {
    if (typeof content === 'object' && content.title && content.content) {
      // Article object format
      return `
        <h1>${this.escapeHtml(content.title)}</h1>
        ${content.topic ? `<p><strong>Topic:</strong> ${this.escapeHtml(content.topic)}</p>` : ''}
        <div class="article-content">
          ${this.formatText(content.content)}
        </div>
      `;
    } else if (typeof content === 'string') {
      // Plain text format
      return this.formatText(content);
    } else {
      return '<p>No content available for export.</p>';
    }
  }

  // Format text content (convert line breaks to paragraphs)
  formatText(text) {
    return text
      .split('\n')
      .filter(line => line.trim())
      .map(line => {
        const trimmed = line.trim();
        if (trimmed.startsWith('# ')) {
          return `<h1>${this.escapeHtml(trimmed.substring(2))}</h1>`;
        } else if (trimmed.startsWith('## ')) {
          return `<h2>${this.escapeHtml(trimmed.substring(3))}</h2>`;
        } else if (trimmed.startsWith('### ')) {
          return `<h3>${this.escapeHtml(trimmed.substring(4))}</h3>`;
        } else if (trimmed.startsWith('- ') || trimmed.startsWith('* ')) {
          return `<li>${this.escapeHtml(trimmed.substring(2))}</li>`;
        } else {
          return `<p>${this.escapeHtml(trimmed)}</p>`;
        }
      })
      .join('\n')
      .replace(/(<li>.*<\/li>\n?)+/g, '<ul>$&</ul>')
      .replace(/<\/li>\n<li>/g, '</li><li>');
  }

  // Escape HTML characters
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  // Strip HTML tags
  stripHtml(html) {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
  }

  // Create export controls UI
  createExportControls(content, container) {
    if (!container) return null;

    const controls = document.createElement('div');
    controls.className = 'export-controls';
    controls.innerHTML = `
      <div class="export-controls-inner">
        <button class="export-btn export-clipboard" title="Copy to Clipboard">
          📋 Copy
        </button>
        <button class="export-btn export-pdf" title="Export as PDF">
          📄 PDF
        </button>
        <button class="export-btn export-word" title="Export as Word">
          📝 Word
        </button>
      </div>
    `;

    // Add event listeners
    const clipboardBtn = controls.querySelector('.export-clipboard');
    const pdfBtn = controls.querySelector('.export-pdf');
    const wordBtn = controls.querySelector('.export-word');

    clipboardBtn.addEventListener('click', async () => {
      const result = await this.copyToClipboard(
        typeof content === 'object' ? content.content : content
      );
      this.showMessage(result.message, result.success ? 'success' : 'error');
    });

    pdfBtn.addEventListener('click', () => {
      const result = this.exportAsPDF(content);
      this.showMessage(result.message, result.success ? 'success' : 'error');
    });

    wordBtn.addEventListener('click', () => {
      const result = this.exportAsWord(content);
      this.showMessage(result.message, result.success ? 'success' : 'error');
    });

    container.appendChild(controls);
    return controls;
  }

  // Show message to user
  showMessage(message, type = 'info') {
    // Create or update message element
    let messageEl = document.querySelector('.export-message');
    if (!messageEl) {
      messageEl = document.createElement('div');
      messageEl.className = 'export-message';
      document.body.appendChild(messageEl);
    }

    messageEl.textContent = message;
    messageEl.className = `export-message ${type}`;
    messageEl.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 16px;
      border-radius: 6px;
      color: white;
      font-weight: 500;
      z-index: 10000;
      max-width: 300px;
      background: ${type === 'success' ? '#047857' : type === 'error' ? '#b91c1c' : '#1d4ed8'};
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      animation: slideInFromRight 0.3s ease-out;
    `;

    // Auto-hide after 3 seconds
    setTimeout(() => {
      if (messageEl) {
        messageEl.style.animation = 'slideOutToRight 0.3s ease-in';
        setTimeout(() => {
          if (messageEl && messageEl.parentNode) {
            messageEl.parentNode.removeChild(messageEl);
          }
        }, 300);
      }
    }, 3000);
  }
}

// Create singleton instance
const exportService = new ExportService();

export default exportService;
