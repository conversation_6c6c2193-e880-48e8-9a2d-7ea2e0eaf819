/* Knowledge Tree Explorer - Optimized CSS (Pareto 80/20) */
/* High contrast, WCAG AAA compliant design system */

:root {
  /* Core Colors - High Contrast WCAG AAA */
  --primary: #1d4ed8;
  --primary-light: #3b82f6;
  --primary-dark: #1e3a8a;
  
  /* Neutral Colors - Maximum Contrast */
  --white: #ffffff;
  --black: #000000;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-500: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* Semantic Colors - High Contrast */
  --success: #047857;
  --warning: #b45309;
  --error: #b91c1c;
  
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  /* Spacing */
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  
  /* Borders & Shadows */
  --radius: 0.5rem;
  --radius-lg: 1rem;
  --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.1);
  
  /* Transitions */
  --transition: 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Reset */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--gray-900);
  background: var(--gray-50);
  -webkit-font-smoothing: antialiased;
}

/* Layout */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-light) 10%);
}

/* Header */
.app-header {
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--space-4) var(--space-6);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-text {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary);
}

/* Buttons */
.btn {
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  text-decoration: none;
  min-height: 44px; /* Touch target */
}

.btn-primary {
  background: var(--primary);
  color: var(--white);
  box-shadow: var(--shadow);
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--gray-100);
  color: var(--gray-800);
  border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
  background: var(--gray-200);
  color: var(--gray-900);
}

/* Forms */
.form-group {
  margin-bottom: var(--space-6);
}

.form-input {
  width: 100%;
  padding: var(--space-4);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius);
  font-size: var(--font-size-base);
  background: var(--white);
  color: var(--gray-900);
  transition: var(--transition);
  min-height: 44px; /* Touch target */
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(29, 78, 216, 0.1);
}

.form-input::placeholder {
  color: var(--gray-500);
}

/* Cards */
.card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  box-shadow: var(--shadow);
  max-width: 600px;
  width: 100%;
}

/* Typography */
.title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
  line-height: 1.1;
}

.subtitle {
  font-size: var(--font-size-lg);
  color: var(--gray-700);
  margin-bottom: var(--space-8);
  line-height: 1.6;
}

.text-center {
  text-align: center;
}

/* Tree View Specific */
.tree-container {
  max-width: 1000px;
  width: 100%;
  padding: var(--space-6);
}

.tree-header {
  text-align: center;
  margin-bottom: var(--space-8);
  background: var(--white);
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
}

.tree-header h1 {
  font-size: var(--font-size-3xl);
  color: var(--gray-900);
  margin-bottom: var(--space-2);
}

.tree-header p {
  color: var(--gray-700);
  font-size: var(--font-size-lg);
}

.branches-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.branch-item {
  background: var(--white);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow);
}

.branch-item:hover {
  border-color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.branch-item.selected {
  border-color: var(--primary);
  background: rgba(29, 78, 216, 0.05);
}

.branch-emoji {
  font-size: var(--font-size-2xl);
  margin-bottom: var(--space-3);
}

.branch-name {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-2);
}

.branch-description {
  color: var(--gray-700);
  line-height: 1.5;
  margin-bottom: var(--space-4);
}

/* Loading States */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  padding: var(--space-8);
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid var(--gray-200);
  border-left-color: var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Error States */
.error {
  background: var(--error);
  color: var(--white);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius);
  margin-bottom: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

/* Success States */
.success {
  background: var(--success);
  color: var(--white);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius);
  margin-bottom: var(--space-6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    padding: var(--space-4);
  }
  
  .card {
    padding: var(--space-6);
  }
  
  .title {
    font-size: var(--font-size-3xl);
  }
  
  .branches-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .header-content {
    padding: 0 var(--space-4);
  }
}

/* Focus States for Accessibility */
.btn:focus-visible,
.form-input:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --gray-500: var(--gray-700);
    --gray-700: var(--gray-800);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
