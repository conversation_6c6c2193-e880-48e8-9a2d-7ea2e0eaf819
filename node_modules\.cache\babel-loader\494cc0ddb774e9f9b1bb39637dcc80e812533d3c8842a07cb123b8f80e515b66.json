{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Appv1\\\\src\\\\components\\\\OptimizedApp.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OptimizedApp = () => {\n  _s();\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [tree, setTree] = useState(null);\n  const [selectedBranch, setSelectedBranch] = useState(null);\n  const [article, setArticle] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const [speechControls, setSpeechControls] = useState(null);\n  const [exportControls, setExportControls] = useState(null);\n  const appRef = useRef(null);\n\n  // Available flags for the optimized version\n  const availableFlags = [{\n    code: '-a',\n    name: 'Article',\n    description: 'Comprehensive article format'\n  }, {\n    code: '-ex',\n    name: 'Examples',\n    description: 'Include practical examples'\n  }, {\n    code: '-q',\n    name: 'Quiz',\n    description: 'Interactive quiz questions'\n  }, {\n    code: '-vis',\n    name: 'Visual',\n    description: 'Include diagrams and visualizations'\n  }, {\n    code: '-path',\n    name: 'Learning Path',\n    description: 'Structured learning progression'\n  }, {\n    code: '-case',\n    name: 'Case Study',\n    description: 'Real-world case studies'\n  }, {\n    code: '-ro',\n    name: 'Romanian',\n    description: 'Adapted for Romanian context'\n  }];\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n    return () => {\n      gestureService.destroy();\n    };\n  }, []);\n\n  // Gesture handlers\n  const handleDoubleTap = (event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData) {\n      // Show flag wheel on double tap of branch\n      const branch = tree === null || tree === void 0 ? void 0 : tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(targetInfo.position, availableFlags, selectedFlags => {\n          console.log('Selected flags:', selectedFlags);\n        }, selectedFlags => {\n          generateArticle(branch, selectedFlags);\n        });\n      }\n    }\n  };\n  const handleSingleTap = (event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData) {\n      const branch = tree === null || tree === void 0 ? void 0 : tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        setSelectedBranch(branch);\n      }\n    }\n  };\n  const handleLongPress = (event, targetInfo) => {\n    // Long press for quick article generation with default flags\n    if (targetInfo.isBranchItem && targetInfo.branchData) {\n      const branch = tree === null || tree === void 0 ? void 0 : tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        generateArticle(branch, ['-a']);\n      }\n    }\n  };\n\n  // Core API call - simplified\n  const generateKnowledgeTree = async topicInput => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Create a knowledge tree for \"${topicInput}\". Return JSON with:\n            {\n              \"tema\": \"${topicInput}\",\n              \"ramuri\": [\n                {\n                  \"nume\": \"Branch Name\",\n                  \"descriere\": \"Brief description\",\n                  \"emoji\": \"📚\",\n                  \"subcategorii\": [\"Sub1\", \"Sub2\", \"Sub3\"]\n                }\n              ]\n            }\n            Provide 6-8 main branches. Keep descriptions concise.`\n          }],\n          temperature: 0.7,\n          max_tokens: 2000\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n\n      // Extract JSON from response\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        const treeData = JSON.parse(jsonMatch[0]);\n        setTree(treeData);\n        setCurrentView('tree');\n\n        // Award points for tree generation\n        const result = gamificationService.awardPoints('TREE_GENERATED');\n        if (result.newAchievements.length > 0) {\n          result.newAchievements.forEach(achievement => {\n            gamificationService.showAchievementNotification(achievement);\n          });\n        }\n      } else {\n        throw new Error('Invalid response format');\n      }\n    } catch (err) {\n      console.error('Error generating tree:', err);\n      setError('Failed to generate knowledge tree. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Generate article for selected branch with flags\n  const generateArticle = async (branch, flags = ['-a']) => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Write a comprehensive article about \"${branch.nume}\" in the context of \"${tree.tema}\".\n\n            Apply these flags: ${flags.join(', ')}\n\n            Flag meanings:\n            - \"-a\": Standard comprehensive article format\n            - \"-ex\": Include 3 practical examples\n            - \"-q\": Add 5 interactive quiz questions at the end\n            - \"-vis\": Describe visual elements and diagrams\n            - \"-path\": Structure as a learning path with steps\n            - \"-case\": Include real-world case studies\n            - \"-ro\": Adapt content for Romanian context and examples\n\n            Make it educational and engaging. Length: 800-1200 words.`\n          }],\n          temperature: 0.7,\n          max_tokens: 3000\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n      const articleData = {\n        title: branch.nume,\n        content: content,\n        topic: tree.tema,\n        flags: flags\n      };\n      setArticle(articleData);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('Error generating article:', err);\n      setError('Failed to generate article. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n    }\n  };\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = branch => {\n    setSelectedBranch(branch);\n    // Don't auto-generate article, wait for double-tap or explicit action\n  };\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article) return;\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n  const handleSpeechRateChange = rate => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleExportWord = () => {\n    if (!article) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleCopyToClipboard = async () => {\n    if (!article) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      setArticle(null);\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n      setTree(null);\n      setSelectedBranch(null);\n    }\n  };\n  const goHome = () => {\n    setCurrentView('input');\n    setTree(null);\n    setSelectedBranch(null);\n    setArticle(null);\n    setTopic('');\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({\n      id: 'dev-1',\n      name: 'Developer',\n      subscriptionTier: 'premium'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    ref: appRef,\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"app-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: goHome,\n          className: \"logo-text\",\n          children: \"\\uD83C\\uDF33 Knowledge Tree Explorer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [user && /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"gamification-container\",\n            style: {\n              marginRight: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 15\n          }, this), !user ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            children: \"\\uD83D\\uDE80 Quick Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Welcome, \", user.name, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-content\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: [\"\\u26A0\\uFE0F \", error, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setError(null),\n          style: {\n            marginLeft: 'auto',\n            background: 'none',\n            border: 'none',\n            color: 'white',\n            cursor: 'pointer'\n          },\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 11\n      }, this), currentView === 'input' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: \"Knowledge Tree Explorer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"subtitle\",\n          children: \"Enter any topic to generate an interactive knowledge tree with AI-powered content.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 13\n        }, this), !user ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f1f5f9',\n            padding: '1rem',\n            borderRadius: '0.5rem',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#334155',\n              marginBottom: '1rem'\n            },\n            children: \"Please log in to access the knowledge tree generator.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            children: \"\\uD83D\\uDE80 Quick Login (Development)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: topic,\n              onChange: e => setTopic(e.target.value),\n              placeholder: \"e.g., Quantum Physics, Machine Learning, History of Art...\",\n              className: \"form-input\",\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isLoading || !topic.trim(),\n            className: \"btn btn-primary\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 23\n              }, this), \"Generating...\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: \"Explore Knowledge \\uD83D\\uDE80\"\n            }, void 0, false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 11\n      }, this), currentView === 'tree' && tree && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tree-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: tree.tema\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Select a branch to explore in detail\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goBack,\n            className: \"btn btn-secondary\",\n            style: {\n              marginTop: '1rem'\n            },\n            children: \"\\u2190 Back to Input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 13\n        }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"branches-grid\",\n          children: tree.ramuri.map((branch, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `branch-item ${selectedBranch === branch ? 'selected' : ''}`,\n            onClick: () => handleBranchSelect(branch),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"branch-emoji\",\n              children: branch.emoji\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"branch-name\",\n              children: branch.nume\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"branch-description\",\n              children: branch.descriere\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 21\n            }, this), branch.subcategorii && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#475569',\n                marginTop: '0.5rem'\n              },\n              children: [\"Topics: \", branch.subcategorii.slice(0, 3).join(', '), branch.subcategorii.length > 3 && '...']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 23\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 11\n      }, this), currentView === 'article' && article && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goBack,\n            className: \"btn btn-secondary\",\n            style: {\n              marginBottom: '2rem'\n            },\n            children: \"\\u2190 Back to Tree\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"title\",\n            children: article.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#475569',\n              marginBottom: '2rem'\n            },\n            children: [\"Part of: \", article.topic]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              lineHeight: '1.8',\n              fontSize: '1.1rem'\n            },\n            children: article.content.split('\\n').map((paragraph, index) => paragraph.trim() && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: paragraph\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 361,\n    columnNumber: 5\n  }, this);\n};\n_s(OptimizedApp, \"ec6QqsY/Nx2Q/sF3/rkn0sqb144=\");\n_c = OptimizedApp;\nexport default OptimizedApp;\nvar _c;\n$RefreshReg$(_c, \"OptimizedApp\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "gestureService", "createFlagWheel", "speechService", "exportService", "gamificationService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OptimizedApp", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "topic", "setTopic", "tree", "setTree", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedBranch", "article", "setArticle", "isLoading", "setIsLoading", "error", "setError", "user", "setUser", "speechControls", "setSpeechControls", "exportControls", "setExportControls", "appRef", "availableFlags", "code", "name", "description", "storedUser", "localStorage", "getItem", "bypassSecurity", "userData", "id", "subscriptionTier", "result", "awardPoints", "newAchievements", "length", "for<PERSON>ach", "achievement", "showAchievementNotification", "current", "init", "doubleTap", "handleDoubleTap", "singleTap", "handleSingleTap", "longPress", "handleLongPress", "destroy", "event", "targetInfo", "isBranchItem", "branchData", "branch", "<PERSON><PERSON>", "index", "position", "selected<PERSON><PERSON><PERSON>", "console", "log", "generateArticle", "generateKnowledgeTree", "topicInput", "response", "fetch", "method", "headers", "process", "env", "REACT_APP_OPENROUTER_API_KEY", "window", "location", "origin", "body", "JSON", "stringify", "model", "messages", "role", "content", "temperature", "max_tokens", "ok", "Error", "status", "data", "json", "choices", "message", "jsonMatch", "match", "treeData", "parse", "err", "flags", "nume", "tema", "join", "articleData", "title", "handleSubmit", "e", "preventDefault", "trim", "handleBranchSelect", "handleSpeechToggle", "getStatus", "isPlaying", "toggle", "speak", "handleSpeechStop", "stop", "handleSpeechRateChange", "rate", "setRate", "handleExportPDF", "exportAsPDF", "replace", "success", "gamResult", "handleExportWord", "exportAsWord", "handleCopyToClipboard", "copyToClipboard", "showMessage", "goBack", "goHome", "quickLogin", "setItem", "className", "ref", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginRight", "marginLeft", "background", "border", "color", "cursor", "padding", "borderRadius", "marginBottom", "onSubmit", "type", "value", "onChange", "target", "placeholder", "disabled", "marginTop", "map", "emoji", "desc<PERSON><PERSON>", "subcategorii", "fontSize", "slice", "lineHeight", "split", "paragraph", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/components/OptimizedApp.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\n\nconst OptimizedApp = () => {\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [tree, setTree] = useState(null);\n  const [selectedBranch, setSelectedBranch] = useState(null);\n  const [article, setArticle] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const [speechControls, setSpeechControls] = useState(null);\n  const [exportControls, setExportControls] = useState(null);\n  const appRef = useRef(null);\n\n  // Available flags for the optimized version\n  const availableFlags = [\n    { code: '-a', name: 'Article', description: 'Comprehensive article format' },\n    { code: '-ex', name: 'Examples', description: 'Include practical examples' },\n    { code: '-q', name: 'Quiz', description: 'Interactive quiz questions' },\n    { code: '-vis', name: 'Visual', description: 'Include diagrams and visualizations' },\n    { code: '-path', name: 'Learning Path', description: 'Structured learning progression' },\n    { code: '-case', name: 'Case Study', description: 'Real-world case studies' },\n    { code: '-ro', name: 'Romanian', description: 'Adapted for Romanian context' }\n  ];\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n\n    return () => {\n      gestureService.destroy();\n    };\n  }, []);\n\n  // Gesture handlers\n  const handleDoubleTap = (event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData) {\n      // Show flag wheel on double tap of branch\n      const branch = tree?.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(\n          targetInfo.position,\n          availableFlags,\n          (selectedFlags) => {\n            console.log('Selected flags:', selectedFlags);\n          },\n          (selectedFlags) => {\n            generateArticle(branch, selectedFlags);\n          }\n        );\n      }\n    }\n  };\n\n  const handleSingleTap = (event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData) {\n      const branch = tree?.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        setSelectedBranch(branch);\n      }\n    }\n  };\n\n  const handleLongPress = (event, targetInfo) => {\n    // Long press for quick article generation with default flags\n    if (targetInfo.isBranchItem && targetInfo.branchData) {\n      const branch = tree?.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        generateArticle(branch, ['-a']);\n      }\n    }\n  };\n\n  // Core API call - simplified\n  const generateKnowledgeTree = async (topicInput) => {\n    setIsLoading(true);\n    setError(null);\n    \n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Create a knowledge tree for \"${topicInput}\". Return JSON with:\n            {\n              \"tema\": \"${topicInput}\",\n              \"ramuri\": [\n                {\n                  \"nume\": \"Branch Name\",\n                  \"descriere\": \"Brief description\",\n                  \"emoji\": \"📚\",\n                  \"subcategorii\": [\"Sub1\", \"Sub2\", \"Sub3\"]\n                }\n              ]\n            }\n            Provide 6-8 main branches. Keep descriptions concise.`\n          }],\n          temperature: 0.7,\n          max_tokens: 2000\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n      \n      // Extract JSON from response\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        const treeData = JSON.parse(jsonMatch[0]);\n        setTree(treeData);\n        setCurrentView('tree');\n\n        // Award points for tree generation\n        const result = gamificationService.awardPoints('TREE_GENERATED');\n        if (result.newAchievements.length > 0) {\n          result.newAchievements.forEach(achievement => {\n            gamificationService.showAchievementNotification(achievement);\n          });\n        }\n      } else {\n        throw new Error('Invalid response format');\n      }\n    } catch (err) {\n      console.error('Error generating tree:', err);\n      setError('Failed to generate knowledge tree. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Generate article for selected branch with flags\n  const generateArticle = async (branch, flags = ['-a']) => {\n    setIsLoading(true);\n    setError(null);\n    \n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Write a comprehensive article about \"${branch.nume}\" in the context of \"${tree.tema}\".\n\n            Apply these flags: ${flags.join(', ')}\n\n            Flag meanings:\n            - \"-a\": Standard comprehensive article format\n            - \"-ex\": Include 3 practical examples\n            - \"-q\": Add 5 interactive quiz questions at the end\n            - \"-vis\": Describe visual elements and diagrams\n            - \"-path\": Structure as a learning path with steps\n            - \"-case\": Include real-world case studies\n            - \"-ro\": Adapt content for Romanian context and examples\n\n            Make it educational and engaging. Length: 800-1200 words.`\n          }],\n          temperature: 0.7,\n          max_tokens: 3000\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n      \n      const articleData = {\n        title: branch.nume,\n        content: content,\n        topic: tree.tema,\n        flags: flags\n      };\n\n      setArticle(articleData);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('Error generating article:', err);\n      setError('Failed to generate article. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle form submission\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n    }\n  };\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = (branch) => {\n    setSelectedBranch(branch);\n    // Don't auto-generate article, wait for double-tap or explicit action\n  };\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article) return;\n\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n\n  const handleSpeechRateChange = (rate) => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleExportWord = () => {\n    if (!article) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleCopyToClipboard = async () => {\n    if (!article) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      setArticle(null);\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n      setTree(null);\n      setSelectedBranch(null);\n    }\n  };\n\n  const goHome = () => {\n    setCurrentView('input');\n    setTree(null);\n    setSelectedBranch(null);\n    setArticle(null);\n    setTopic('');\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({ id: 'dev-1', name: 'Developer', subscriptionTier: 'premium' });\n  };\n\n  return (\n    <div className=\"app\" ref={appRef}>\n      {/* Header */}\n      <header className=\"app-header\">\n        <div className=\"header-content\">\n          <button onClick={goHome} className=\"logo-text\">\n            🌳 Knowledge Tree Explorer\n          </button>\n          <div className=\"header-right\">\n            {user && (\n              <div id=\"gamification-container\" style={{ marginRight: '16px' }}>\n                {/* Gamification UI will be inserted here */}\n              </div>\n            )}\n            {!user ? (\n              <button onClick={quickLogin} className=\"btn btn-primary\">\n                🚀 Quick Login\n              </button>\n            ) : (\n              <span>Welcome, {user.name}!</span>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"main-content\">\n        {error && (\n          <div className=\"error\">\n            ⚠️ {error}\n            <button onClick={() => setError(null)} style={{marginLeft: 'auto', background: 'none', border: 'none', color: 'white', cursor: 'pointer'}}>\n              ✕\n            </button>\n          </div>\n        )}\n\n        {/* Topic Input View */}\n        {currentView === 'input' && (\n          <div className=\"card text-center\">\n            <h1 className=\"title\">Knowledge Tree Explorer</h1>\n            <p className=\"subtitle\">\n              Enter any topic to generate an interactive knowledge tree with AI-powered content.\n            </p>\n            \n            {!user ? (\n              <div style={{background: '#f1f5f9', padding: '1rem', borderRadius: '0.5rem', marginBottom: '2rem'}}>\n                <p style={{color: '#334155', marginBottom: '1rem'}}>\n                  Please log in to access the knowledge tree generator.\n                </p>\n                <button onClick={quickLogin} className=\"btn btn-primary\">\n                  🚀 Quick Login (Development)\n                </button>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit}>\n                <div className=\"form-group\">\n                  <input\n                    type=\"text\"\n                    value={topic}\n                    onChange={(e) => setTopic(e.target.value)}\n                    placeholder=\"e.g., Quantum Physics, Machine Learning, History of Art...\"\n                    className=\"form-input\"\n                    disabled={isLoading}\n                  />\n                </div>\n                <button \n                  type=\"submit\" \n                  disabled={isLoading || !topic.trim()} \n                  className=\"btn btn-primary\"\n                >\n                  {isLoading ? (\n                    <>\n                      <span className=\"spinner\"></span>\n                      Generating...\n                    </>\n                  ) : (\n                    <>\n                      Explore Knowledge 🚀\n                    </>\n                  )}\n                </button>\n              </form>\n            )}\n          </div>\n        )}\n\n        {/* Tree View */}\n        {currentView === 'tree' && tree && (\n          <div className=\"tree-container\">\n            <div className=\"tree-header\">\n              <h1>{tree.tema}</h1>\n              <p>Select a branch to explore in detail</p>\n              <button onClick={goBack} className=\"btn btn-secondary\" style={{marginTop: '1rem'}}>\n                ← Back to Input\n              </button>\n            </div>\n\n            {isLoading ? (\n              <div className=\"loading\">\n                <span className=\"spinner\"></span>\n                <span>Loading...</span>\n              </div>\n            ) : (\n              <div className=\"branches-grid\">\n                {tree.ramuri.map((branch, index) => (\n                  <div\n                    key={index}\n                    className={`branch-item ${selectedBranch === branch ? 'selected' : ''}`}\n                    onClick={() => handleBranchSelect(branch)}\n                  >\n                    <div className=\"branch-emoji\">{branch.emoji}</div>\n                    <h3 className=\"branch-name\">{branch.nume}</h3>\n                    <p className=\"branch-description\">{branch.descriere}</p>\n                    {branch.subcategorii && (\n                      <div style={{fontSize: '0.875rem', color: '#475569', marginTop: '0.5rem'}}>\n                        Topics: {branch.subcategorii.slice(0, 3).join(', ')}\n                        {branch.subcategorii.length > 3 && '...'}\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Article View */}\n        {currentView === 'article' && article && (\n          <div className=\"tree-container\">\n            <div className=\"card\">\n              <button onClick={goBack} className=\"btn btn-secondary\" style={{marginBottom: '2rem'}}>\n                ← Back to Tree\n              </button>\n              \n              <h1 className=\"title\">{article.title}</h1>\n              <p style={{color: '#475569', marginBottom: '2rem'}}>\n                Part of: {article.topic}\n              </p>\n              \n              <div style={{lineHeight: '1.8', fontSize: '1.1rem'}}>\n                {article.content.split('\\n').map((paragraph, index) => (\n                  paragraph.trim() && (\n                    <p key={index} style={{marginBottom: '1rem'}}>\n                      {paragraph}\n                    </p>\n                  )\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n};\n\nexport default OptimizedApp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAChC,OAAOC,cAAc,IAAIC,eAAe,QAAQ,4BAA4B;AAC5E,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,mBAAmB,MAAM,0CAA0C;;AAE1E;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACzD,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4B,IAAI,EAAEC,OAAO,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC8B,cAAc,EAAEC,iBAAiB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAMkC,MAAM,GAAGhC,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAMiC,cAAc,GAAG,CACrB;IAAEC,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAE;EAA+B,CAAC,EAC5E;IAAEF,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAE;EAA6B,CAAC,EAC5E;IAAEF,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAE;EAA6B,CAAC,EACvE;IAAEF,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,QAAQ;IAAEC,WAAW,EAAE;EAAsC,CAAC,EACpF;IAAEF,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,eAAe;IAAEC,WAAW,EAAE;EAAkC,CAAC,EACxF;IAAEF,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,YAAY;IAAEC,WAAW,EAAE;EAA0B,CAAC,EAC7E;IAAEF,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAE;EAA+B,CAAC,CAC/E;;EAED;EACArC,SAAS,CAAC,MAAM;IACd,MAAMsC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,MAAMC,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAE7D,IAAIF,UAAU,IAAIG,cAAc,EAAE;MAChC,MAAMC,QAAQ,GAAG;QACfC,EAAE,EAAE,QAAQ;QACZP,IAAI,EAAE,MAAM;QACZQ,gBAAgB,EAAE;MACpB,CAAC;MACDhB,OAAO,CAACc,QAAQ,CAAC;;MAEjB;MACA,MAAMG,MAAM,GAAGvC,mBAAmB,CAACwC,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C5C,mBAAmB,CAAC6C,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAIjB,MAAM,CAACmB,OAAO,EAAE;MAClBlD,cAAc,CAACmD,IAAI,CAACpB,MAAM,CAACmB,OAAO,EAAE;QAClCE,SAAS,EAAEC,eAAe;QAC1BC,SAAS,EAAEC,eAAe;QAC1BC,SAAS,EAAEC;MACb,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACXzD,cAAc,CAAC0D,OAAO,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAML,eAAe,GAAGA,CAACM,KAAK,EAAEC,UAAU,KAAK;IAC7C,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,EAAE;MACpD;MACA,MAAMC,MAAM,GAAGhD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,MAAM,CAACJ,UAAU,CAACE,UAAU,CAACG,KAAK,CAAC;MACxD,IAAIF,MAAM,EAAE;QACV9D,eAAe,CACb2D,UAAU,CAACM,QAAQ,EACnBlC,cAAc,EACbmC,aAAa,IAAK;UACjBC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,aAAa,CAAC;QAC/C,CAAC,EACAA,aAAa,IAAK;UACjBG,eAAe,CAACP,MAAM,EAAEI,aAAa,CAAC;QACxC,CACF,CAAC;MACH;IACF;EACF,CAAC;EAED,MAAMZ,eAAe,GAAGA,CAACI,KAAK,EAAEC,UAAU,KAAK;IAC7C;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,EAAE;MACpD,MAAMC,MAAM,GAAGhD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,MAAM,CAACJ,UAAU,CAACE,UAAU,CAACG,KAAK,CAAC;MACxD,IAAIF,MAAM,EAAE;QACV7C,iBAAiB,CAAC6C,MAAM,CAAC;MAC3B;IACF;EACF,CAAC;EAED,MAAMN,eAAe,GAAGA,CAACE,KAAK,EAAEC,UAAU,KAAK;IAC7C;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,EAAE;MACpD,MAAMC,MAAM,GAAGhD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,MAAM,CAACJ,UAAU,CAACE,UAAU,CAACG,KAAK,CAAC;MACxD,IAAIF,MAAM,EAAE;QACVO,eAAe,CAACP,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC;MACjC;IACF;EACF,CAAC;;EAED;EACA,MAAMQ,qBAAqB,GAAG,MAAOC,UAAU,IAAK;IAClDlD,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMiD,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,OAAO,CAACC,GAAG,CAACC,4BAA4B,EAAE;UACrE,cAAc,EAAE,kBAAkB;UAClC,cAAc,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM;UACtC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,gCAAgC;UACvCC,QAAQ,EAAE,CAAC;YACTC,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,gCAAgCjB,UAAU;AAC/D;AACA,yBAAyBA,UAAU;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACU,CAAC,CAAC;UACFkB,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAAClB,QAAQ,CAACmB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,cAAcpB,QAAQ,CAACqB,MAAM,EAAE,CAAC;MAClD;MAEA,MAAMC,IAAI,GAAG,MAAMtB,QAAQ,CAACuB,IAAI,CAAC,CAAC;MAClC,MAAMP,OAAO,GAAGM,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAACT,OAAO;;MAE/C;MACA,MAAMU,SAAS,GAAGV,OAAO,CAACW,KAAK,CAAC,aAAa,CAAC;MAC9C,IAAID,SAAS,EAAE;QACb,MAAME,QAAQ,GAAGjB,IAAI,CAACkB,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;QACzCnF,OAAO,CAACqF,QAAQ,CAAC;QACjBzF,cAAc,CAAC,MAAM,CAAC;;QAEtB;QACA,MAAM+B,MAAM,GAAGvC,mBAAmB,CAACwC,WAAW,CAAC,gBAAgB,CAAC;QAChE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;UACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;YAC5C5C,mBAAmB,CAAC6C,2BAA2B,CAACD,WAAW,CAAC;UAC9D,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,MAAM,IAAI6C,KAAK,CAAC,yBAAyB,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZnC,OAAO,CAAC7C,KAAK,CAAC,wBAAwB,EAAEgF,GAAG,CAAC;MAC5C/E,QAAQ,CAAC,sDAAsD,CAAC;IAClE,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMgD,eAAe,GAAG,MAAAA,CAAOP,MAAM,EAAEyC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK;IACxDlF,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMiD,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,OAAO,CAACC,GAAG,CAACC,4BAA4B,EAAE;UACrE,cAAc,EAAE,kBAAkB;UAClC,cAAc,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM;UACtC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,gCAAgC;UACvCC,QAAQ,EAAE,CAAC;YACTC,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,wCAAwC1B,MAAM,CAAC0C,IAAI,wBAAwB1F,IAAI,CAAC2F,IAAI;AACzG;AACA,iCAAiCF,KAAK,CAACG,IAAI,CAAC,IAAI,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACU,CAAC,CAAC;UACFjB,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAAClB,QAAQ,CAACmB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,cAAcpB,QAAQ,CAACqB,MAAM,EAAE,CAAC;MAClD;MAEA,MAAMC,IAAI,GAAG,MAAMtB,QAAQ,CAACuB,IAAI,CAAC,CAAC;MAClC,MAAMP,OAAO,GAAGM,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAACT,OAAO;MAE/C,MAAMmB,WAAW,GAAG;QAClBC,KAAK,EAAE9C,MAAM,CAAC0C,IAAI;QAClBhB,OAAO,EAAEA,OAAO;QAChB5E,KAAK,EAAEE,IAAI,CAAC2F,IAAI;QAChBF,KAAK,EAAEA;MACT,CAAC;MAEDpF,UAAU,CAACwF,WAAW,CAAC;MACvBhG,cAAc,CAAC,SAAS,CAAC;;MAEzB;MACA,MAAM+B,MAAM,GAAGvC,mBAAmB,CAACwC,WAAW,CAAC,mBAAmB,CAAC;MACnE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C5C,mBAAmB,CAAC6C,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOuD,GAAG,EAAE;MACZnC,OAAO,CAAC7C,KAAK,CAAC,2BAA2B,EAAEgF,GAAG,CAAC;MAC/C/E,QAAQ,CAAC,+CAA+C,CAAC;IAC3D,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMwF,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAInG,KAAK,CAACoG,IAAI,CAAC,CAAC,EAAE;MAChB1C,qBAAqB,CAAC1D,KAAK,CAACoG,IAAI,CAAC,CAAC,CAAC;IACrC;EACF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAInD,MAAM,IAAK;IACrC7C,iBAAiB,CAAC6C,MAAM,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMoD,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAAChG,OAAO,EAAE;IAEd,IAAIjB,aAAa,CAACkH,SAAS,CAAC,CAAC,CAACC,SAAS,EAAE;MACvCnH,aAAa,CAACoH,MAAM,CAAC,CAAC;IACxB,CAAC,MAAM;MACLpH,aAAa,CAACqH,KAAK,CAACpG,OAAO,CAACsE,OAAO,CAAC;MACpC;MACA,MAAM9C,MAAM,GAAGvC,mBAAmB,CAACwC,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5C5C,mBAAmB,CAAC6C,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMwE,gBAAgB,GAAGA,CAAA,KAAM;IAC7BtH,aAAa,CAACuH,IAAI,CAAC,CAAC;EACtB,CAAC;EAED,MAAMC,sBAAsB,GAAIC,IAAI,IAAK;IACvCzH,aAAa,CAAC0H,OAAO,CAACD,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAC1G,OAAO,EAAE;IACd,MAAMwB,MAAM,GAAGxC,aAAa,CAAC2H,WAAW,CAAC3G,OAAO,EAAE,GAAGA,OAAO,CAAC0F,KAAK,CAACkB,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACnG,IAAIpF,MAAM,CAACqF,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAG7H,mBAAmB,CAACwC,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIqF,SAAS,CAACpF,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCmF,SAAS,CAACpF,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/C5C,mBAAmB,CAAC6C,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMkF,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAAC/G,OAAO,EAAE;IACd,MAAMwB,MAAM,GAAGxC,aAAa,CAACgI,YAAY,CAAChH,OAAO,EAAE,GAAGA,OAAO,CAAC0F,KAAK,CAACkB,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACpG,IAAIpF,MAAM,CAACqF,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAG7H,mBAAmB,CAACwC,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIqF,SAAS,CAACpF,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCmF,SAAS,CAACpF,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/C5C,mBAAmB,CAAC6C,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMoF,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAACjH,OAAO,EAAE;IACd,MAAMwB,MAAM,GAAG,MAAMxC,aAAa,CAACkI,eAAe,CAAClH,OAAO,CAACsE,OAAO,CAAC;IACnEtF,aAAa,CAACmI,WAAW,CAAC3F,MAAM,CAACuD,OAAO,EAAEvD,MAAM,CAACqF,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;IAC/E,IAAIrF,MAAM,CAACqF,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAG7H,mBAAmB,CAACwC,WAAW,CAAC,aAAa,CAAC;MAChE,IAAIqF,SAAS,CAACpF,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCmF,SAAS,CAACpF,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/C5C,mBAAmB,CAAC6C,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA,MAAMuF,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAI5H,WAAW,KAAK,SAAS,EAAE;MAC7BC,cAAc,CAAC,MAAM,CAAC;MACtBQ,UAAU,CAAC,IAAI,CAAC;IAClB,CAAC,MAAM,IAAIT,WAAW,KAAK,MAAM,EAAE;MACjCC,cAAc,CAAC,OAAO,CAAC;MACvBI,OAAO,CAAC,IAAI,CAAC;MACbE,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC;EAED,MAAMsH,MAAM,GAAGA,CAAA,KAAM;IACnB5H,cAAc,CAAC,OAAO,CAAC;IACvBI,OAAO,CAAC,IAAI,CAAC;IACbE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,UAAU,CAAC,IAAI,CAAC;IAChBN,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;;EAED;EACA,MAAM2H,UAAU,GAAGA,CAAA,KAAM;IACvBpG,YAAY,CAACqG,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAC9ChH,OAAO,CAAC;MAAEe,EAAE,EAAE,OAAO;MAAEP,IAAI,EAAE,WAAW;MAAEQ,gBAAgB,EAAE;IAAU,CAAC,CAAC;EAC1E,CAAC;EAED,oBACEpC,OAAA;IAAKqI,SAAS,EAAC,KAAK;IAACC,GAAG,EAAE7G,MAAO;IAAA8G,QAAA,gBAE/BvI,OAAA;MAAQqI,SAAS,EAAC,YAAY;MAAAE,QAAA,eAC5BvI,OAAA;QAAKqI,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BvI,OAAA;UAAQwI,OAAO,EAAEN,MAAO;UAACG,SAAS,EAAC,WAAW;UAAAE,QAAA,EAAC;QAE/C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5I,OAAA;UAAKqI,SAAS,EAAC,cAAc;UAAAE,QAAA,GAC1BpH,IAAI,iBACHnB,OAAA;YAAKmC,EAAE,EAAC,wBAAwB;YAAC0G,KAAK,EAAE;cAAEC,WAAW,EAAE;YAAO;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE3D,CACN,EACA,CAACzH,IAAI,gBACJnB,OAAA;YAAQwI,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAAC;UAEzD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAET5I,OAAA;YAAAuI,QAAA,GAAM,WAAS,EAACpH,IAAI,CAACS,IAAI,EAAC,GAAC;UAAA;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAClC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGT5I,OAAA;MAAMqI,SAAS,EAAC,cAAc;MAAAE,QAAA,GAC3BtH,KAAK,iBACJjB,OAAA;QAAKqI,SAAS,EAAC,OAAO;QAAAE,QAAA,GAAC,eAClB,EAACtH,KAAK,eACTjB,OAAA;UAAQwI,OAAO,EAAEA,CAAA,KAAMtH,QAAQ,CAAC,IAAI,CAAE;UAAC2H,KAAK,EAAE;YAACE,UAAU,EAAE,MAAM;YAAEC,UAAU,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEC,KAAK,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAS,CAAE;UAAAZ,QAAA,EAAC;QAE3I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAvI,WAAW,KAAK,OAAO,iBACtBL,OAAA;QAAKqI,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/BvI,OAAA;UAAIqI,SAAS,EAAC,OAAO;UAAAE,QAAA,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClD5I,OAAA;UAAGqI,SAAS,EAAC,UAAU;UAAAE,QAAA,EAAC;QAExB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAEH,CAACzH,IAAI,gBACJnB,OAAA;UAAK6I,KAAK,EAAE;YAACG,UAAU,EAAE,SAAS;YAAEI,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAf,QAAA,gBACjGvI,OAAA;YAAG6I,KAAK,EAAE;cAACK,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE;YAAM,CAAE;YAAAf,QAAA,EAAC;UAEpD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ5I,OAAA;YAAQwI,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAAC;UAEzD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAEN5I,OAAA;UAAMuJ,QAAQ,EAAE/C,YAAa;UAAA+B,QAAA,gBAC3BvI,OAAA;YAAKqI,SAAS,EAAC,YAAY;YAAAE,QAAA,eACzBvI,OAAA;cACEwJ,IAAI,EAAC,MAAM;cACXC,KAAK,EAAElJ,KAAM;cACbmJ,QAAQ,EAAGjD,CAAC,IAAKjG,QAAQ,CAACiG,CAAC,CAACkD,MAAM,CAACF,KAAK,CAAE;cAC1CG,WAAW,EAAC,4DAA4D;cACxEvB,SAAS,EAAC,YAAY;cACtBwB,QAAQ,EAAE9I;YAAU;cAAA0H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5I,OAAA;YACEwJ,IAAI,EAAC,QAAQ;YACbK,QAAQ,EAAE9I,SAAS,IAAI,CAACR,KAAK,CAACoG,IAAI,CAAC,CAAE;YACrC0B,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAE1BxH,SAAS,gBACRf,OAAA,CAAAE,SAAA;cAAAqI,QAAA,gBACEvI,OAAA;gBAAMqI,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,iBAEnC;YAAA,eAAE,CAAC,gBAEH5I,OAAA,CAAAE,SAAA;cAAAqI,QAAA,EAAE;YAEF,gBAAE;UACH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGAvI,WAAW,KAAK,MAAM,IAAII,IAAI,iBAC7BT,OAAA;QAAKqI,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BvI,OAAA;UAAKqI,SAAS,EAAC,aAAa;UAAAE,QAAA,gBAC1BvI,OAAA;YAAAuI,QAAA,EAAK9H,IAAI,CAAC2F;UAAI;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpB5I,OAAA;YAAAuI,QAAA,EAAG;UAAoC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC3C5I,OAAA;YAAQwI,OAAO,EAAEP,MAAO;YAACI,SAAS,EAAC,mBAAmB;YAACQ,KAAK,EAAE;cAACiB,SAAS,EAAE;YAAM,CAAE;YAAAvB,QAAA,EAAC;UAEnF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL7H,SAAS,gBACRf,OAAA;UAAKqI,SAAS,EAAC,SAAS;UAAAE,QAAA,gBACtBvI,OAAA;YAAMqI,SAAS,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjC5I,OAAA;YAAAuI,QAAA,EAAM;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,gBAEN5I,OAAA;UAAKqI,SAAS,EAAC,eAAe;UAAAE,QAAA,EAC3B9H,IAAI,CAACiD,MAAM,CAACqG,GAAG,CAAC,CAACtG,MAAM,EAAEE,KAAK,kBAC7B3D,OAAA;YAEEqI,SAAS,EAAE,eAAe1H,cAAc,KAAK8C,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;YACxE+E,OAAO,EAAEA,CAAA,KAAM5B,kBAAkB,CAACnD,MAAM,CAAE;YAAA8E,QAAA,gBAE1CvI,OAAA;cAAKqI,SAAS,EAAC,cAAc;cAAAE,QAAA,EAAE9E,MAAM,CAACuG;YAAK;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClD5I,OAAA;cAAIqI,SAAS,EAAC,aAAa;cAAAE,QAAA,EAAE9E,MAAM,CAAC0C;YAAI;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9C5I,OAAA;cAAGqI,SAAS,EAAC,oBAAoB;cAAAE,QAAA,EAAE9E,MAAM,CAACwG;YAAS;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACvDnF,MAAM,CAACyG,YAAY,iBAClBlK,OAAA;cAAK6I,KAAK,EAAE;gBAACsB,QAAQ,EAAE,UAAU;gBAAEjB,KAAK,EAAE,SAAS;gBAAEY,SAAS,EAAE;cAAQ,CAAE;cAAAvB,QAAA,GAAC,UACjE,EAAC9E,MAAM,CAACyG,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC/D,IAAI,CAAC,IAAI,CAAC,EAClD5C,MAAM,CAACyG,YAAY,CAAC1H,MAAM,GAAG,CAAC,IAAI,KAAK;YAAA;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CACN;UAAA,GAZIjF,KAAK;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGAvI,WAAW,KAAK,SAAS,IAAIQ,OAAO,iBACnCb,OAAA;QAAKqI,SAAS,EAAC,gBAAgB;QAAAE,QAAA,eAC7BvI,OAAA;UAAKqI,SAAS,EAAC,MAAM;UAAAE,QAAA,gBACnBvI,OAAA;YAAQwI,OAAO,EAAEP,MAAO;YAACI,SAAS,EAAC,mBAAmB;YAACQ,KAAK,EAAE;cAACS,YAAY,EAAE;YAAM,CAAE;YAAAf,QAAA,EAAC;UAEtF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET5I,OAAA;YAAIqI,SAAS,EAAC,OAAO;YAAAE,QAAA,EAAE1H,OAAO,CAAC0F;UAAK;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1C5I,OAAA;YAAG6I,KAAK,EAAE;cAACK,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE;YAAM,CAAE;YAAAf,QAAA,GAAC,WACzC,EAAC1H,OAAO,CAACN,KAAK;UAAA;YAAAkI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAEJ5I,OAAA;YAAK6I,KAAK,EAAE;cAACwB,UAAU,EAAE,KAAK;cAAEF,QAAQ,EAAE;YAAQ,CAAE;YAAA5B,QAAA,EACjD1H,OAAO,CAACsE,OAAO,CAACmF,KAAK,CAAC,IAAI,CAAC,CAACP,GAAG,CAAC,CAACQ,SAAS,EAAE5G,KAAK,KAChD4G,SAAS,CAAC5D,IAAI,CAAC,CAAC,iBACd3G,OAAA;cAAe6I,KAAK,EAAE;gBAACS,YAAY,EAAE;cAAM,CAAE;cAAAf,QAAA,EAC1CgC;YAAS,GADJ5G,KAAK;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CAEN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACxI,EAAA,CAvfID,YAAY;AAAAqK,EAAA,GAAZrK,YAAY;AAyflB,eAAeA,YAAY;AAAC,IAAAqK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}