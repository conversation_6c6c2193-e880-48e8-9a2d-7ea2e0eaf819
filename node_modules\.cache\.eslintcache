[{"C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\App.jsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\storageService.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\config\\subscriptionConfig.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\openRouterService.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\gamificationService.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\ArticleView.jsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\GestureHandler.jsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\TreeView.jsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\AuthModal.jsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\user\\UserDashboard.jsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\subscription\\SubscriptionPlans.jsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\contexts\\AuthContext.jsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\config\\gamificationConfig.js": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\authService.js": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\config\\flagsConfig.js": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\EnhancedContentRenderer.jsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\RegisterForm.jsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\LoginForm.jsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\Leaderboard.jsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\UserStats.jsx": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\AchievementBadges.jsx": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\SocialLoginButtons.jsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\ApiTest.jsx": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\SecurityGuard.jsx": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\securityService.js": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\DeveloperPanel.jsx": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\config\\developer.js": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\OptimizedApp.jsx": "29"}, {"size": 254, "mtime": 1749767918369, "results": "30", "hashOfConfig": "31"}, {"size": 366, "mtime": 1749810318779, "results": "32", "hashOfConfig": "31"}, {"size": 5127, "mtime": 1749796765840, "results": "33", "hashOfConfig": "31"}, {"size": 9157, "mtime": 1749769973576, "results": "34", "hashOfConfig": "31"}, {"size": 12310, "mtime": 1749797668677, "results": "35", "hashOfConfig": "31"}, {"size": 9388, "mtime": 1749796318089, "results": "36", "hashOfConfig": "31"}, {"size": 6891, "mtime": 1749769328999, "results": "37", "hashOfConfig": "31"}, {"size": 8232, "mtime": 1749767564759, "results": "38", "hashOfConfig": "31"}, {"size": 8536, "mtime": 1749769469544, "results": "39", "hashOfConfig": "31"}, {"size": 1848, "mtime": 1749770265422, "results": "40", "hashOfConfig": "31"}, {"size": 11871, "mtime": 1749796753284, "results": "41", "hashOfConfig": "31"}, {"size": 7910, "mtime": 1749770381376, "results": "42", "hashOfConfig": "31"}, {"size": 10534, "mtime": 1749805213740, "results": "43", "hashOfConfig": "31"}, {"size": 7802, "mtime": 1749796275735, "results": "44", "hashOfConfig": "31"}, {"size": 12441, "mtime": 1749808074806, "results": "45", "hashOfConfig": "31"}, {"size": 11730, "mtime": 1749769404185, "results": "46", "hashOfConfig": "31"}, {"size": 12219, "mtime": 1749769263532, "results": "47", "hashOfConfig": "31"}, {"size": 10485, "mtime": 1749770142474, "results": "48", "hashOfConfig": "31"}, {"size": 5374, "mtime": 1749770106823, "results": "49", "hashOfConfig": "31"}, {"size": 7673, "mtime": 1749796347435, "results": "50", "hashOfConfig": "31"}, {"size": 8820, "mtime": 1749796419889, "results": "51", "hashOfConfig": "31"}, {"size": 8956, "mtime": 1749796490173, "results": "52", "hashOfConfig": "31"}, {"size": 7126, "mtime": 1749770190872, "results": "53", "hashOfConfig": "31"}, {"size": 8292, "mtime": 1749797907137, "results": "54", "hashOfConfig": "31"}, {"size": 13889, "mtime": 1749808134139, "results": "55", "hashOfConfig": "31"}, {"size": 9229, "mtime": 1749802391818, "results": "56", "hashOfConfig": "31"}, {"size": 10024, "mtime": 1749808248203, "results": "57", "hashOfConfig": "31"}, {"size": 2905, "mtime": 1749806181235, "results": "58", "hashOfConfig": "31"}, {"size": 10870, "mtime": 1749810136657, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "x402fp", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\storageService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\config\\subscriptionConfig.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\openRouterService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\gamificationService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\ArticleView.jsx", ["147"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\GestureHandler.jsx", ["148", "149"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\TreeView.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\AuthModal.jsx", ["150"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\user\\UserDashboard.jsx", ["151"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\subscription\\SubscriptionPlans.jsx", ["152", "153", "154"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\contexts\\AuthContext.jsx", ["155"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\config\\gamificationConfig.js", ["156"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\authService.js", ["157"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\config\\flagsConfig.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\EnhancedContentRenderer.jsx", ["158"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\RegisterForm.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\LoginForm.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\Leaderboard.jsx", ["159"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\UserStats.jsx", ["160"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\AchievementBadges.jsx", ["161"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\SocialLoginButtons.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\ApiTest.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\SecurityGuard.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\securityService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\DeveloperPanel.jsx", ["162", "163"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\config\\developer.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\OptimizedApp.jsx", [], [], {"ruleId": "164", "severity": 1, "message": "165", "line": 194, "column": 10, "nodeType": "166", "messageId": "167", "endLine": 194, "endColumn": 30}, {"ruleId": "168", "severity": 1, "message": "169", "line": 181, "column": 7, "nodeType": "170", "messageId": "171", "endLine": 213, "endColumn": 8}, {"ruleId": "172", "severity": 1, "message": "173", "line": 229, "column": 6, "nodeType": "174", "endLine": 229, "endColumn": 89, "suggestions": "175"}, {"ruleId": "172", "severity": 1, "message": "176", "line": 45, "column": 6, "nodeType": "174", "endLine": 45, "endColumn": 14, "suggestions": "177"}, {"ruleId": "164", "severity": 1, "message": "178", "line": 3, "column": 30, "nodeType": "166", "messageId": "167", "endLine": 3, "endColumn": 52}, {"ruleId": "164", "severity": 1, "message": "179", "line": 9, "column": 10, "nodeType": "166", "messageId": "167", "endLine": 9, "endColumn": 22}, {"ruleId": "164", "severity": 1, "message": "180", "line": 10, "column": 10, "nodeType": "166", "messageId": "167", "endLine": 10, "endColumn": 26}, {"ruleId": "164", "severity": 1, "message": "181", "line": 28, "column": 9, "nodeType": "166", "messageId": "167", "endLine": 28, "endColumn": 29}, {"ruleId": "164", "severity": 1, "message": "182", "line": 5, "column": 29, "nodeType": "166", "messageId": "167", "endLine": 5, "endColumn": 45}, {"ruleId": "168", "severity": 1, "message": "169", "line": 224, "column": 5, "nodeType": "170", "messageId": "171", "endLine": 240, "endColumn": 6}, {"ruleId": "164", "severity": 1, "message": "183", "line": 2, "column": 10, "nodeType": "166", "messageId": "167", "endLine": 2, "endColumn": 28}, {"ruleId": "164", "severity": 1, "message": "184", "line": 1, "column": 20, "nodeType": "166", "messageId": "167", "endLine": 1, "endColumn": 29}, {"ruleId": "172", "severity": 1, "message": "185", "line": 17, "column": 6, "nodeType": "174", "endLine": 17, "endColumn": 24, "suggestions": "186"}, {"ruleId": "164", "severity": 1, "message": "187", "line": 8, "column": 11, "nodeType": "166", "messageId": "167", "endLine": 8, "endColumn": 15}, {"ruleId": "164", "severity": 1, "message": "188", "line": 19, "column": 13, "nodeType": "166", "messageId": "167", "endLine": 19, "endColumn": 22}, {"ruleId": "164", "severity": 1, "message": "189", "line": 6, "column": 17, "nodeType": "166", "messageId": "167", "endLine": 6, "endColumn": 25}, {"ruleId": "172", "severity": 1, "message": "190", "line": 58, "column": 6, "nodeType": "174", "endLine": 58, "endColumn": 8, "suggestions": "191"}, "no-unused-vars", "'formatArticleContent' is defined but never used.", "Identifier", "unusedVar", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'showDebug'. Either include it or remove the dependency array. You can also do a functional update 'setShowDebug(s => ...)' if you only need 'showDebug' in the 'setShowDebug' call.", "ArrayExpression", ["192"], "React Hook useEffect has a missing dependency: 'handleKeyDown'. Either include it or remove the dependency array.", ["193"], "'getUserPermissionLevel' is defined but never used.", "'selectedPlan' is assigned a value but never used.", "'showPaymentModal' is assigned a value but never used.", "'handlePaymentSuccess' is assigned a value but never used.", "'DEVELOPER_CONFIG' is defined but never used.", "'SUBSCRIPTION_TIERS' is defined but never used.", "'useEffect' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadLeaderboardData'. Either include it or remove the dependency array.", ["194"], "'user' is assigned a value but never used.", "'userStats' is assigned a value but never used.", "'setIsDev' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'hasAutoShown'. Either include it or remove the dependency array.", ["195"], {"desc": "196", "fix": "197"}, {"desc": "198", "fix": "199"}, {"desc": "200", "fix": "201"}, {"desc": "202", "fix": "203"}, "Update the dependencies array to be: [onVerticalScroll, onReverseScroll, onLateralScroll, onVGesture, onSGesture, onTap, showDebug]", {"range": "204", "text": "205"}, "Update the dependencies array to be: [handleKeyDown, isOpen]", {"range": "206", "text": "207"}, "Update the dependencies array to be: [loadLeaderboardData, selectedCategory]", {"range": "208", "text": "209"}, "Update the dependencies array to be: [hasAutoShown]", {"range": "210", "text": "211"}, [7195, 7278], "[onVerticalScroll, onReverseScroll, onLateralScroll, onVGesture, onSGesture, onTap, showDebug]", [1034, 1042], "[handleKeyDown, isOpen]", [688, 706], "[loadLeaderboardData, selectedCategory]", [2193, 2195], "[hasAutoShown]"]