[{"C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\App.jsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\storageService.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\openRouterService.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\gamificationService.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\AuthModal.jsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\user\\UserDashboard.jsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\subscription\\SubscriptionPlans.jsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\RegisterForm.jsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\LoginForm.jsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\Leaderboard.jsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\UserStats.jsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\AchievementBadges.jsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\SocialLoginButtons.jsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\OptimizedApp.jsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\exportService.js": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\gestureService.js": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\speechService.js": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\optimizedGamificationService.js": "19"}, {"size": 254, "mtime": 1749767918369, "results": "20", "hashOfConfig": "21"}, {"size": 366, "mtime": 1749810318779, "results": "22", "hashOfConfig": "21"}, {"size": 5127, "mtime": 1749796765840, "results": "23", "hashOfConfig": "21"}, {"size": 12310, "mtime": 1749797668677, "results": "24", "hashOfConfig": "21"}, {"size": 9388, "mtime": 1749796318089, "results": "25", "hashOfConfig": "21"}, {"size": 1848, "mtime": 1749770265422, "results": "26", "hashOfConfig": "21"}, {"size": 11871, "mtime": 1749796753284, "results": "27", "hashOfConfig": "21"}, {"size": 7910, "mtime": 1749770381376, "results": "28", "hashOfConfig": "21"}, {"size": 10485, "mtime": 1749770142474, "results": "29", "hashOfConfig": "21"}, {"size": 5374, "mtime": 1749770106823, "results": "30", "hashOfConfig": "21"}, {"size": 7673, "mtime": 1749796347435, "results": "31", "hashOfConfig": "21"}, {"size": 8820, "mtime": 1749796419889, "results": "32", "hashOfConfig": "21"}, {"size": 8956, "mtime": 1749796490173, "results": "33", "hashOfConfig": "21"}, {"size": 7126, "mtime": 1749770190872, "results": "34", "hashOfConfig": "21"}, {"size": 22252, "mtime": 1749811529773, "results": "35", "hashOfConfig": "21"}, {"size": 12724, "mtime": 1749811062481, "results": "36", "hashOfConfig": "21"}, {"size": 8700, "mtime": 1749810971983, "results": "37", "hashOfConfig": "21"}, {"size": 8881, "mtime": 1749811012254, "results": "38", "hashOfConfig": "21"}, {"size": 13410, "mtime": 1749811140435, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "x402fp", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\storageService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\openRouterService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\gamificationService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\AuthModal.jsx", ["97"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\user\\UserDashboard.jsx", ["98"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\subscription\\SubscriptionPlans.jsx", ["99", "100", "101"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\RegisterForm.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\LoginForm.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\Leaderboard.jsx", ["102"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\UserStats.jsx", ["103"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\gamification\\AchievementBadges.jsx", ["104"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\auth\\SocialLoginButtons.jsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\components\\OptimizedApp.jsx", ["105", "106", "107", "108", "109", "110"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\exportService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\gestureService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\speechService.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Appv1\\src\\services\\optimizedGamificationService.js", [], [], {"ruleId": "111", "severity": 1, "message": "112", "line": 45, "column": 6, "nodeType": "113", "endLine": 45, "endColumn": 14, "suggestions": "114"}, {"ruleId": "115", "severity": 1, "message": "116", "line": 3, "column": 30, "nodeType": "117", "messageId": "118", "endLine": 3, "endColumn": 52}, {"ruleId": "115", "severity": 1, "message": "119", "line": 9, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 9, "endColumn": 22}, {"ruleId": "115", "severity": 1, "message": "120", "line": 10, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 10, "endColumn": 26}, {"ruleId": "115", "severity": 1, "message": "121", "line": 28, "column": 9, "nodeType": "117", "messageId": "118", "endLine": 28, "endColumn": 29}, {"ruleId": "111", "severity": 1, "message": "122", "line": 17, "column": 6, "nodeType": "113", "endLine": 17, "endColumn": 24, "suggestions": "123"}, {"ruleId": "115", "severity": 1, "message": "124", "line": 8, "column": 11, "nodeType": "117", "messageId": "118", "endLine": 8, "endColumn": 15}, {"ruleId": "115", "severity": 1, "message": "125", "line": 19, "column": 13, "nodeType": "117", "messageId": "118", "endLine": 19, "endColumn": 22}, {"ruleId": "111", "severity": 1, "message": "126", "line": 23, "column": 9, "nodeType": "127", "endLine": 31, "endColumn": 4}, {"ruleId": "128", "severity": 1, "message": "129", "line": 67, "column": 7, "nodeType": "117", "messageId": "130", "endLine": 67, "endColumn": 22}, {"ruleId": "128", "severity": 1, "message": "131", "line": 67, "column": 24, "nodeType": "117", "messageId": "130", "endLine": 67, "endColumn": 39}, {"ruleId": "128", "severity": 1, "message": "132", "line": 67, "column": 41, "nodeType": "117", "messageId": "130", "endLine": 67, "endColumn": 56}, {"ruleId": "128", "severity": 1, "message": "133", "line": 100, "column": 29, "nodeType": "117", "messageId": "130", "endLine": 100, "endColumn": 44}, {"ruleId": "128", "severity": 1, "message": "133", "line": 120, "column": 13, "nodeType": "117", "messageId": "130", "endLine": 120, "endColumn": 28}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleKeyDown'. Either include it or remove the dependency array.", "ArrayExpression", ["134"], "no-unused-vars", "'getUserPermissionLevel' is defined but never used.", "Identifier", "unusedVar", "'selectedPlan' is assigned a value but never used.", "'showPaymentModal' is assigned a value but never used.", "'handlePaymentSuccess' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadLeaderboardData'. Either include it or remove the dependency array.", ["135"], "'user' is assigned a value but never used.", "'userStats' is assigned a value but never used.", "The 'availableFlags' array makes the dependencies of useCallback Hook (at line 100) change on every render. Move it inside the useCallback callback. Alternatively, wrap the initialization of 'availableFlags' in its own useMemo() Hook.", "VariableDeclarator", "no-use-before-define", "'handleDoubleTap' was used before it was defined.", "usedBeforeDefined", "'handleSingleTap' was used before it was defined.", "'handleLongPress' was used before it was defined.", "'generateArticle' was used before it was defined.", {"desc": "136", "fix": "137"}, {"desc": "138", "fix": "139"}, "Update the dependencies array to be: [handleKeyDown, isOpen]", {"range": "140", "text": "141"}, "Update the dependencies array to be: [loadLeaderboardData, selectedCategory]", {"range": "142", "text": "143"}, [1034, 1042], "[handleKeyDown, isOpen]", [688, 706], "[loadLeaderboardData, selectedCategory]"]