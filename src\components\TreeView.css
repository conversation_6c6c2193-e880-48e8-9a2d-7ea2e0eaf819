/* TreeView Component Styles */
.tree-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  animation: fadeIn 0.5s ease-out;
}

.tree-header {
  text-align: center;
  margin-bottom: 30px;
  color: #1e293b; /* High contrast dark text instead of white */
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tree-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.tree-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 0;
}

/* Branches Container */
.branches-container {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Branch Item */
.branch-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
}

.branch-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: rgba(102, 126, 234, 0.3);
}

.branch-item.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.branch-emoji {
  font-size: 2.5rem;
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12px;
}

.branch-content {
  flex: 1;
  min-width: 0;
}

.branch-name {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #0f172a; /* Much darker for better contrast */
}

.branch-description {
  color: #334155; /* Much darker for better contrast */
  font-size: 1rem;
  line-height: 1.4;
  margin-bottom: 10px;
}

.branch-subcategories {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.subcategory-preview {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
}

.subcategory-more {
  background: rgba(118, 75, 162, 0.1);
  color: #764ba2;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
}

.branch-arrow {
  font-size: 1.2rem;
  color: #667eea;
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.branch-item.selected .branch-arrow {
  transform: rotate(90deg);
}

/* Branch Details */
.branch-details {
  max-width: 800px;
  margin: 30px auto 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  animation: slideUp 0.3s ease-out;
}

.selected-branch-info h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: #0f172a; /* Much darker for better contrast */
  display: flex;
  align-items: center;
  gap: 10px;
}

.selected-branch-info p {
  color: #334155; /* Much darker for better contrast */
  font-size: 1.1rem;
  line-height: 1.5;
  margin-bottom: 20px;
}

.subcategories h4 {
  color: #0f172a; /* Much darker for better contrast */
  margin-bottom: 10px;
  font-size: 1rem;
}

.subcategory-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 20px;
}

.subcategory-tag {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.generate-btn, .flags-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.generate-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.flags-btn.secondary {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 2px solid rgba(102, 126, 234, 0.2);
}

.generate-btn:hover, .flags-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
}

/* Flags Panel */
.flags-panel {
  border-top: 2px solid rgba(102, 126, 234, 0.1);
  padding-top: 20px;
  animation: slideDown 0.3s ease-out;
  max-height: 70vh;
  overflow-y: auto;
}

.flags-panel h4 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1.2rem;
}

.flags-categories {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
}

.flag-category {
  background: rgba(102, 126, 234, 0.02);
  border-radius: 12px;
  padding: 15px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.category-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  padding-bottom: 8px;
}

.category-icon {
  font-size: 1.2rem;
}

.category-count {
  font-size: 0.8rem;
  color: #666;
  background: rgba(102, 126, 234, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: auto;
}

.flags-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 10px;
}

.flag-option {
  background: rgba(102, 126, 234, 0.05);
  border: 2px solid rgba(102, 126, 234, 0.1);
  border-radius: 10px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.flag-option:hover {
  border-color: rgba(102, 126, 234, 0.3);
  background: rgba(102, 126, 234, 0.1);
}

.flag-option.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.2);
}

.flag-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.flag-label {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #667eea;
  font-size: 0.9rem;
}

.flag-time {
  font-size: 0.75rem;
  color: #999;
  background: rgba(102, 126, 234, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
}

.flag-name {
  font-weight: 600;
  margin: 4px 0;
  color: #333;
  font-size: 0.9rem;
}

.flag-desc {
  font-size: 0.8rem;
  color: #666;
  line-height: 1.3;
  margin-bottom: 6px;
}

.flag-complexity {
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
  text-align: center;
  margin-top: 4px;
}

.complexity-low {
  background: rgba(81, 207, 102, 0.1);
  color: #51cf66;
  border: 1px solid rgba(81, 207, 102, 0.2);
}

.complexity-medium {
  background: rgba(255, 212, 59, 0.1);
  color: #ffd43b;
  border: 1px solid rgba(255, 212, 59, 0.2);
}

.complexity-high {
  background: rgba(255, 107, 107, 0.1);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.2);
}

.selected-flags {
  padding: 15px;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 8px;
  color: #333;
  margin-bottom: 20px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.flags-summary {
  font-family: 'Courier New', monospace;
  margin-bottom: 10px;
}

.flags-metadata {
  display: flex;
  gap: 15px;
  font-size: 0.85rem;
  color: #666;
}

.estimated-time,
.complexity-level {
  display: flex;
  align-items: center;
  gap: 4px;
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.default-flag {
  color: #999;
  font-style: italic;
}

.popular-combinations {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(118, 75, 162, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(118, 75, 162, 0.1);
}

.popular-combinations h6 {
  margin-bottom: 10px;
  color: #333;
  font-size: 0.9rem;
  font-weight: 600;
}

.combo-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.combo-btn {
  background: rgba(118, 75, 162, 0.1);
  color: #764ba2;
  border: 1px solid rgba(118, 75, 162, 0.2);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.combo-btn:hover {
  background: rgba(118, 75, 162, 0.15);
  border-color: rgba(118, 75, 162, 0.3);
  transform: translateY(-1px);
}

.flag-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid rgba(102, 126, 234, 0.1);
}

.clear-flags-btn,
.select-all-btn {
  padding: 8px 16px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 8px;
  background: rgba(102, 126, 234, 0.05);
  color: #667eea;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-flags-btn:hover,
.select-all-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.4);
  transform: translateY(-1px);
}

.select-all-btn {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-color: rgba(118, 75, 162, 0.2);
  color: #764ba2;
}

.select-all-btn:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
  border-color: rgba(118, 75, 162, 0.4);
}

/* Gesture Hints */
.gesture-hints {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

.hint {
  display: flex;
  align-items: center;
  gap: 10px;
}

.gesture {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: bold;
  min-width: 24px;
  text-align: center;
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 500px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .tree-view {
    padding: 15px;
  }
  
  .tree-header h1 {
    font-size: 2rem;
  }
  
  .branch-item {
    padding: 15px;
  }
  
  .branch-emoji {
    font-size: 2rem;
    width: 50px;
    height: 50px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .flags-grid {
    grid-template-columns: 1fr;
  }
  
  .gesture-hints {
    position: relative;
    bottom: auto;
    right: auto;
    margin-top: 20px;
  }
}

@media (max-width: 480px) {
  .branch-details {
    padding: 20px 15px;
  }
  
  .flags-panel {
    padding-top: 15px;
  }
}
