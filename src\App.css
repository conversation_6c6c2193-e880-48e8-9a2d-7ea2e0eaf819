/* Knowledge Tree Explorer - Professional Enterprise Design System */

/* Optimized Design Tokens - WCAG AAA Compliant Colors */
:root {
  /* Primary Colors - High Contrast Blue Palette */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Neutral Colors - High Contrast Gray Scale */
  --neutral-0: #ffffff;
  --neutral-50: #f8fafc;
  --neutral-100: #f1f5f9;
  --neutral-200: #e2e8f0;
  --neutral-300: #cbd5e1;
  --neutral-400: #64748b; /* Increased contrast from #94a3b8 */
  --neutral-500: #475569; /* Increased contrast from #64748b */
  --neutral-600: #334155; /* Increased contrast from #475569 */
  --neutral-700: #1e293b; /* Increased contrast from #334155 */
  --neutral-800: #0f172a; /* Increased contrast from #1e293b */
  --neutral-900: #000000; /* Maximum contrast from #0f172a */

  /* Semantic Colors - High Contrast WCAG AAA */
  --success-500: #059669; /* Darker green for better contrast */
  --success-600: #047857;
  --warning-500: #d97706; /* Darker orange for better contrast */
  --warning-600: #b45309;
  --error-500: #dc2626; /* Darker red for better contrast */
  --error-600: #b91c1c;

  /* Typography Scale */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;

  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Spacing Scale */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */

  /* Border Radius */
  --radius-sm: 0.375rem;   /* 6px */
  --radius-base: 0.5rem;   /* 8px */
  --radius-md: 0.75rem;    /* 12px */
  --radius-lg: 1rem;       /* 16px */
  --radius-xl: 1.5rem;     /* 24px */
  --radius-full: 9999px;

  /* Shadows - Professional Depth */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-base: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-md: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Z-Index Scale */
  --z-base: 0;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
  --z-toast: 1070;
}

/* Global Reset & Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: var(--font-family-sans);
  line-height: 1.6;
  color: var(--neutral-800);
  background-color: var(--neutral-50);
}

/* Main App Container - Professional Layout */
.app {
  min-height: 100vh;
  min-height: 100dvh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
  position: relative;
  overflow-x: hidden;
}

/* Professional Header - Enterprise Design */
.app-header {
  background: var(--neutral-0);
  border-bottom: 1px solid var(--neutral-200);
  padding: var(--space-4) var(--space-6);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.95);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-6);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  text-decoration: none;
  color: var(--neutral-800);
  transition: var(--transition-fast);
}

.logo-section:hover {
  color: var(--primary-600);
}

.logo-icon {
  font-size: var(--font-size-2xl);
  color: var(--primary-600);
}

.logo-text {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--neutral-800);
}

.header-nav {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.nav-button {
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: var(--transition-fast);
  border: 1px solid transparent;
  cursor: pointer;
  background: none;
  font-family: inherit;
}

.nav-button.primary {
  background: var(--primary-600);
  color: var(--neutral-0);
  border-color: var(--primary-600);
}

.nav-button.primary:hover {
  background: var(--primary-700);
  border-color: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.nav-button.secondary {
  background: var(--neutral-100);
  color: var(--neutral-700);
  border-color: var(--neutral-200);
}

.nav-button.secondary:hover {
  background: var(--neutral-200);
  color: var(--neutral-800);
}

.nav-button.ghost {
  color: var(--neutral-600);
}

.nav-button.ghost:hover {
  background: var(--neutral-100);
  color: var(--neutral-800);
}

.user-menu {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background: var(--primary-100);
  color: var(--primary-700);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
}

/* Enhanced Error Message - Professional Design */
.error-message {
  position: fixed;
  top: var(--space-6);
  left: 50%;
  transform: translateX(-50%);
  background: var(--error-500);
  color: var(--neutral-0);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-toast);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  animation: slideDown var(--transition-base);
  max-width: calc(100vw - var(--space-8));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.error-message button {
  background: none;
  border: none;
  color: var(--neutral-0);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--space-1);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-base);
  transition: var(--transition-fast);
  outline-offset: 2px;
}

.error-message button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.error-message button:focus-visible {
  outline: 2px solid var(--neutral-0);
}

@keyframes slideDown {
  from {
    transform: translateX(-50%) translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

/* Main Content Area - Professional Enterprise Layout */
.main-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-6);
  min-height: calc(100vh - 80px);
  background: linear-gradient(135deg, var(--neutral-50) 0%, var(--primary-50) 100%);
  position: relative;
}

.main-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.5;
  z-index: 0;
}

/* Security: Authentication-based access control */
[data-auth-required="true"]:not(.authenticated-user) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

.auth-required {
  display: none !important;
  visibility: hidden !important;
}

.auth-required.authenticated-user {
  display: block !important;
  visibility: visible !important;
}

/* Professional Topic Input Card */
.topic-input-card {
  background: var(--neutral-0);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-xl);
  padding: var(--space-12) var(--space-10);
  max-width: 700px;
  width: 100%;
  text-align: center;
  box-shadow: var(--shadow-lg);
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.98);
}

.topic-input-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

/* Professional Typography */
.hero-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--neutral-900);
  margin-bottom: var(--space-4);
  line-height: 1.1;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, var(--neutral-900) 0%, var(--primary-700) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  color: var(--neutral-600);
  font-size: var(--font-size-lg);
  line-height: 1.6;
  margin-bottom: var(--space-8);
  max-width: 480px;
  margin-left: auto;
  margin-right: auto;
  font-weight: var(--font-weight-normal);
}

/* Professional Form Styling */
.topic-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  width: 100%;
}

/* Professional Input Field */
.topic-input-field {
  padding: var(--space-4) var(--space-5);
  border: 2px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  font-family: var(--font-family-sans);
  transition: var(--transition-fast);
  background: var(--neutral-0);
  color: var(--neutral-800);
  width: 100%;
  min-height: 56px;
  appearance: none;
  -webkit-appearance: none;
  box-shadow: var(--shadow-sm);
  outline: none;
  position: relative;
}

.topic-input-field:hover {
  border-color: var(--neutral-300);
  box-shadow: var(--shadow-base);
}

.topic-input-field:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--shadow-base);
  background: var(--neutral-0);
}

.topic-input-field:disabled {
  background: var(--neutral-100);
  color: var(--neutral-500);
  cursor: not-allowed;
  opacity: 0.6;
  border-color: var(--neutral-200);
}

.topic-input-field::placeholder {
  color: var(--neutral-500);
  opacity: 1;
}

/* Professional Primary Button */
.primary-button {
  padding: var(--space-4) var(--space-8);
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  color: var(--neutral-0);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  font-family: var(--font-family-sans);
  cursor: pointer;
  transition: var(--transition-fast);
  position: relative;
  overflow: hidden;
  min-height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  box-shadow: var(--shadow-base);
  border: 1px solid var(--primary-600);
}

.primary-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  transition: left var(--transition-slow);
}

.primary-button:hover::before {
  left: 100%;
}

.primary-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-800) 100%);
  border-color: var(--primary-700);
}

.primary-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-base);
}

.primary-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-sm);
  background: var(--neutral-300);
  border-color: var(--neutral-300);
}

.primary-button:focus-visible {
  outline: 2px solid var(--primary-300);
  outline-offset: 2px;
}

.button-icon {
  font-size: var(--font-size-lg);
  transition: var(--transition-fast);
}

.primary-button:hover .button-icon {
  transform: translateX(2px);
}

/* Professional Gesture Hints Card */
.gesture-hints-card {
  margin-top: var(--space-8);
  padding: var(--space-6);
  background: var(--neutral-50);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  text-align: left;
  transition: var(--transition-fast);
  position: relative;
}

.gesture-hints-card:hover {
  background: var(--neutral-100);
  transform: translateY(-1px);
  box-shadow: var(--shadow-base);
}

.gesture-hints-title {
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-4);
  color: var(--neutral-800);
  font-size: var(--font-size-base);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.gesture-hints-title::before {
  content: '⚡';
  font-size: var(--font-size-lg);
  color: var(--primary-600);
}

.gesture-hints-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  gap: var(--space-3);
}

.gesture-hint-item {
  color: var(--neutral-600);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  transition: var(--transition-fast);
  padding: var(--space-2);
  border-radius: var(--radius-base);
}

.gesture-hint-item:hover {
  color: var(--neutral-800);
  background: var(--neutral-100);
  transform: translateX(2px);
}

.gesture-hint-icon {
  font-size: var(--font-size-base);
  opacity: 0.8;
  min-width: 20px;
  text-align: center;
}

/* Professional Loading States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 80px);
  padding: var(--space-6);
  flex: 1;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 3px solid var(--neutral-200);
  border-left: 3px solid var(--primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-6);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--neutral-600);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-align: center;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* Professional Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(24px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Professional Responsive Design - Mobile First Approach */

/* Tablet Portrait (768px and up) */
@media (min-width: 768px) {
  .main-content {
    padding: var(--space-12) var(--space-8);
  }

  .topic-input-card {
    padding: var(--space-16) var(--space-12);
    max-width: 600px;
  }

  .hero-title {
    font-size: var(--font-size-5xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-xl);
  }

  .header-content {
    padding: 0 var(--space-8);
  }

  .gesture-hints-list {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4);
  }
}

/* Desktop (1024px and up) */
@media (min-width: 1024px) {
  .main-content {
    padding: var(--space-16) var(--space-10);
  }

  .topic-input-card {
    max-width: 700px;
    padding: var(--space-20) var(--space-16);
  }

  .header-content {
    max-width: 1200px;
  }
}

/* Large Desktop (1280px and up) */
@media (min-width: 1280px) {
  .topic-input-card {
    max-width: 800px;
  }

  .header-content {
    max-width: 1400px;
  }
}

/* Mobile Optimizations - Professional Mobile Experience */
@media (max-width: 767px) {
  .main-content {
    padding: var(--space-6) var(--space-4);
    min-height: calc(100vh - 72px);
  }

  .topic-input-card {
    padding: var(--space-8) var(--space-6);
    margin: var(--space-4) 0;
    border-radius: var(--radius-lg);
  }

  .hero-title {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--space-3);
  }

  .hero-subtitle {
    font-size: var(--font-size-base);
    margin-bottom: var(--space-6);
  }

  .topic-input-field {
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-base);
    min-height: 48px;
  }

  .primary-button {
    padding: var(--space-3) var(--space-6);
    font-size: var(--font-size-base);
    min-height: 48px;
  }

  .gesture-hints-card {
    margin-top: var(--space-6);
    padding: var(--space-4);
  }

  .gesture-hints-title {
    font-size: var(--font-size-sm);
  }

  .gesture-hint-item {
    font-size: var(--font-size-xs);
    padding: var(--space-1);
  }

  .header-content {
    padding: 0 var(--space-4);
    gap: var(--space-4);
  }

  .nav-button {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-xs);
  }

  .logo-text {
    font-size: var(--font-size-lg);
  }

  /* Mobile Auth Overlay */
  .auth-card {
    padding: var(--space-8);
    margin: var(--space-4);
  }

  .auth-card h2 {
    font-size: var(--font-size-xl);
  }

  .auth-buttons {
    gap: var(--space-3);
  }

  .auth-button {
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-sm);
  }
}

/* Extra Small Devices (phones, 480px and down) */
@media (max-width: 480px) {
  .main-content {
    padding: var(--space-4) var(--space-3);
  }

  .topic-input-card {
    padding: var(--space-6) var(--space-4);
    margin: var(--space-2) 0;
  }

  .hero-title {
    font-size: var(--font-size-2xl);
  }

  .topic-input-field,
  .primary-button {
    font-size: var(--font-size-sm);
    min-height: 44px;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
  }

  .loading-text {
    font-size: var(--font-size-sm);
  }

  .header-content {
    padding: 0 var(--space-3);
  }

  .logo-text {
    display: none;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .topic-input-card {
    border-width: 0.5px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --neutral-0: #0f172a;
    --neutral-50: #1e293b;
    --neutral-100: #334155;
    --neutral-900: #f8fafc;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Professional Authentication Overlay */
.auth-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  backdrop-filter: blur(10px);
}

.auth-card {
  background: var(--neutral-0);
  border-radius: var(--radius-xl);
  padding: var(--space-12);
  max-width: 400px;
  width: 90%;
  box-shadow: var(--shadow-xl);
  text-align: center;
  animation: scaleIn 0.3s ease-out;
}

.auth-card h2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--neutral-800);
  margin-bottom: var(--space-4);
}

.auth-card p {
  color: var(--neutral-600);
  margin-bottom: var(--space-8);
  line-height: 1.6;
}

.auth-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.auth-button {
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  border: none;
  cursor: pointer;
  transition: var(--transition-fast);
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.auth-button.primary {
  background: var(--primary-600);
  color: var(--neutral-0);
}

.auth-button.primary:hover {
  background: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-base);
}

.auth-button.secondary {
  background: var(--neutral-100);
  color: var(--neutral-700);
  border: 1px solid var(--neutral-200);
}

.auth-button.secondary:hover {
  background: var(--neutral-200);
  color: var(--neutral-800);
}

/* Professional Focus States */
.primary-button:focus-visible,
.nav-button:focus-visible,
.auth-button:focus-visible {
  outline: 2px solid var(--primary-300);
  outline-offset: 2px;
}

/* Professional Hover Effects */
.topic-input-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
  transition: var(--transition-base);
}

/* Professional Loading Animation */
.loading-spinner {
  border-color: var(--neutral-200);
  border-left-color: var(--primary-600);
}

/* Professional Error States */
.error-message {
  animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Professional Success States */
.success-message {
  position: fixed;
  top: var(--space-6);
  right: var(--space-6);
  background: var(--success-500);
  color: var(--neutral-0);
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-toast);
  animation: slideInFromTop 0.3s ease-out;
}

/* Professional Micro-Interactions */
.topic-input-field:focus {
  transform: scale(1.01);
}

.primary-button:active {
  transform: scale(0.98);
}

.nav-button:active {
  transform: scale(0.95);
}

.auth-button:active {
  transform: scale(0.98);
}

/* Professional Glassmorphism Effects */
.topic-input-card {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.app-header {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
}

/* Professional Scroll Behavior */
html {
  scroll-behavior: smooth;
}

/* Professional Selection Styles */
::selection {
  background: var(--primary-200);
  color: var(--primary-800);
}

::-moz-selection {
  background: var(--primary-200);
  color: var(--primary-800);
}

/* Professional Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-100);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-300);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-400);
}

/* Professional Print Styles */
@media print {
  .app-header,
  .gesture-hints-card,
  .auth-overlay,
  .nav-button {
    display: none !important;
  }

  .main-content {
    padding: 0;
    background: white;
  }

  .topic-input-card {
    box-shadow: none;
    border: 1px solid var(--neutral-300);
  }
}

/* Landscape Mobile Optimization */
@media (orientation: landscape) and (max-height: 600px) {
  .topic-input-container {
    min-height: 100vh;
    padding: var(--spacing-4) var(--spacing-6);
  }

  .topic-input {
    padding: var(--spacing-6) var(--spacing-8);
  }

  .topic-input h1 {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-2);
  }

  .topic-input p {
    margin-bottom: var(--spacing-4);
  }

  .gesture-hints {
    margin-top: var(--spacing-4);
    padding: var(--spacing-3);
  }
}

/* Professional Navigation Header - Clean Design */
.app-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 32px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  height: 72px;
}

.nav-left {
  display: flex;
  align-items: center;
}

.nav-logo {
  background: none;
  border: none;
  font-size: 20px;
  font-weight: 700;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  color: #3b82f6;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
  outline-offset: 2px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-logo:hover {
  color: var(--primary-dark);
  background: var(--glass-white-light);
  transform: scale(1.05);
}

.nav-logo:focus-visible {
  outline: 2px solid var(--primary-color);
}

.nav-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}

.user-greeting {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  padding: var(--spacing-2);
  border-radius: var(--radius-lg);
  background: var(--glass-white-light);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-white);
  transition: all var(--transition-base);
}

.user-greeting:hover {
  background: var(--glass-white);
  transform: scale(1.02);
}

.nav-btn {
  padding: var(--spacing-2) var(--spacing-4);
  border: none;
  border-radius: var(--radius-full);
  background: var(--primary-gradient);
  color: var(--white);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-base);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-1-5);
  min-height: 40px;
  min-width: 40px;
  position: relative;
  overflow: hidden;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  white-space: nowrap;
  outline-offset: 2px;
}

.nav-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.nav-btn:hover::before {
  left: 100%;
}

.nav-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-primary);
  background: var(--primary-gradient-dark);
}

.nav-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.nav-btn:focus-visible {
  outline: 2px solid var(--white);
  outline-offset: 2px;
}

.nav-btn.primary {
  background: var(--primary-gradient);
}

.nav-btn.primary:hover {
  background: var(--primary-gradient-dark);
}

.nav-btn.subscription {
  background: linear-gradient(135deg, var(--accent-color) 0%, var(--warning-color) 100%);
  animation: pulse 2s infinite;
}

.nav-btn.subscription:hover {
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
  background: linear-gradient(135deg, var(--accent-dark) 0%, var(--warning-dark) 100%);
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.auth-prompt {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 16px;
  margin-top: 24px;
  text-align: center;
}

.auth-prompt p {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
}

.link-btn {
  background: none;
  border: none;
  color: #ffd43b;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
}

.link-btn:hover {
  color: #ffed4e;
}

/* Enhanced Responsive Navigation */
@media (min-width: 768px) {
  .app-navigation {
    padding: var(--spacing-5) var(--spacing-8);
  }

  .nav-right {
    gap: var(--spacing-4);
  }
}

@media (max-width: 767px) {
  .app-navigation {
    padding: 12px 16px;
    height: 64px;
  }

  .nav-logo {
    font-size: 18px;
    padding: 4px 8px;
  }

  .nav-right {
    gap: 8px;
  }

  .nav-btn {
    padding: 8px 12px;
    font-size: 14px;
    min-height: 40px;
    min-width: 40px;
  }

  .user-greeting {
    display: none;
  }
}

@media (max-width: 480px) {
  .app-navigation {
    padding: var(--spacing-2) var(--spacing-3);
    min-height: 52px;
  }

  .nav-logo {
    font-size: var(--font-size-base);
  }

  .nav-right {
    gap: var(--spacing-1-5);
  }

  .nav-btn {
    padding: var(--spacing-1-5) var(--spacing-2-5);
    font-size: var(--font-size-xs);
    min-height: 32px;
    min-width: 32px;
  }

  .nav-btn span {
    display: none; /* Hide text on very small screens */
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .nav-btn {
    min-height: 44px;
    min-width: 44px;
  }

  .topic-input-field {
    min-height: 44px;
  }

  .topic-input button {
    min-height: 44px;
  }
}
