{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Appv1\\\\src\\\\App.jsx\";\nimport React from 'react';\nimport OptimizedApp from './components/OptimizedApp';\nimport './styles/optimized.css';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Simplified architecture focusing on core functionality\n\n// Optimized App - Single component with essential features\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function App() {\n  return /*#__PURE__*/_jsxDEV(OptimizedApp, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 10\n  }, this);\n}\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "OptimizedApp", "jsxDEV", "_jsxDEV", "App", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/App.jsx"], "sourcesContent": ["import React from 'react';\nimport OptimizedApp from './components/OptimizedApp';\nimport './styles/optimized.css';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Simplified architecture focusing on core functionality\n\n// Optimized App - Single component with essential features\nexport default function App() {\n  return <OptimizedApp />;\n}\n\n\n\n\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAO,wBAAwB;;AAE/B;AACA;;AAEA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,eAAe,SAASC,GAAGA,CAAA,EAAG;EAC5B,oBAAOD,OAAA,CAACF,YAAY;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACzB;AAACC,EAAA,GAFuBL,GAAG;AAAA,IAAAK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}