import React, { useState, useEffect } from 'react';
import '../styles/optimized.css';

// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation
// Focus on core functionality with maximum impact

const OptimizedApp = () => {
  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'
  const [topic, setTopic] = useState('');
  const [tree, setTree] = useState(null);
  const [selectedBranch, setSelectedBranch] = useState(null);
  const [article, setArticle] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [user, setUser] = useState(null);

  // Simplified authentication check
  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    const bypassSecurity = localStorage.getItem('bypassSecurity');
    
    if (storedUser || bypassSecurity) {
      setUser({ 
        id: 'user-1', 
        name: 'User', 
        subscriptionTier: 'premium' 
      });
    }
  }, []);

  // Core API call - simplified
  const generateKnowledgeTree = async (topicInput) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Knowledge Tree Explorer'
        },
        body: JSON.stringify({
          model: 'deepseek/deepseek-r1-0528:free',
          messages: [{
            role: 'user',
            content: `Create a knowledge tree for "${topicInput}". Return JSON with:
            {
              "tema": "${topicInput}",
              "ramuri": [
                {
                  "nume": "Branch Name",
                  "descriere": "Brief description",
                  "emoji": "📚",
                  "subcategorii": ["Sub1", "Sub2", "Sub3"]
                }
              ]
            }
            Provide 6-8 main branches. Keep descriptions concise.`
          }],
          temperature: 0.7,
          max_tokens: 2000
        })
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }

      const data = await response.json();
      const content = data.choices[0].message.content;
      
      // Extract JSON from response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const treeData = JSON.parse(jsonMatch[0]);
        setTree(treeData);
        setCurrentView('tree');
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      console.error('Error generating tree:', err);
      setError('Failed to generate knowledge tree. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Generate article for selected branch
  const generateArticle = async (branch) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Knowledge Tree Explorer'
        },
        body: JSON.stringify({
          model: 'deepseek/deepseek-r1-0528:free',
          messages: [{
            role: 'user',
            content: `Write a comprehensive article about "${branch.nume}" in the context of "${tree.tema}".
            
            Include:
            - Introduction
            - Key concepts
            - Practical examples
            - Conclusion
            
            Make it educational and engaging. Length: 800-1200 words.`
          }],
          temperature: 0.7,
          max_tokens: 3000
        })
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }

      const data = await response.json();
      const content = data.choices[0].message.content;
      
      setArticle({
        title: branch.nume,
        content: content,
        topic: tree.tema
      });
      setCurrentView('article');
    } catch (err) {
      console.error('Error generating article:', err);
      setError('Failed to generate article. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    if (topic.trim()) {
      generateKnowledgeTree(topic.trim());
    }
  };

  // Handle branch selection
  const handleBranchSelect = (branch) => {
    setSelectedBranch(branch);
    generateArticle(branch);
  };

  // Navigation functions
  const goBack = () => {
    if (currentView === 'article') {
      setCurrentView('tree');
      setArticle(null);
    } else if (currentView === 'tree') {
      setCurrentView('input');
      setTree(null);
      setSelectedBranch(null);
    }
  };

  const goHome = () => {
    setCurrentView('input');
    setTree(null);
    setSelectedBranch(null);
    setArticle(null);
    setTopic('');
  };

  // Quick login for development
  const quickLogin = () => {
    localStorage.setItem('bypassSecurity', 'true');
    setUser({ id: 'dev-1', name: 'Developer', subscriptionTier: 'premium' });
  };

  return (
    <div className="app">
      {/* Header */}
      <header className="app-header">
        <div className="header-content">
          <button onClick={goHome} className="logo-text">
            🌳 Knowledge Tree Explorer
          </button>
          <div>
            {!user ? (
              <button onClick={quickLogin} className="btn btn-primary">
                🚀 Quick Login
              </button>
            ) : (
              <span>Welcome, {user.name}!</span>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="main-content">
        {error && (
          <div className="error">
            ⚠️ {error}
            <button onClick={() => setError(null)} style={{marginLeft: 'auto', background: 'none', border: 'none', color: 'white', cursor: 'pointer'}}>
              ✕
            </button>
          </div>
        )}

        {/* Topic Input View */}
        {currentView === 'input' && (
          <div className="card text-center">
            <h1 className="title">Knowledge Tree Explorer</h1>
            <p className="subtitle">
              Enter any topic to generate an interactive knowledge tree with AI-powered content.
            </p>
            
            {!user ? (
              <div style={{background: '#f1f5f9', padding: '1rem', borderRadius: '0.5rem', marginBottom: '2rem'}}>
                <p style={{color: '#334155', marginBottom: '1rem'}}>
                  Please log in to access the knowledge tree generator.
                </p>
                <button onClick={quickLogin} className="btn btn-primary">
                  🚀 Quick Login (Development)
                </button>
              </div>
            ) : (
              <form onSubmit={handleSubmit}>
                <div className="form-group">
                  <input
                    type="text"
                    value={topic}
                    onChange={(e) => setTopic(e.target.value)}
                    placeholder="e.g., Quantum Physics, Machine Learning, History of Art..."
                    className="form-input"
                    disabled={isLoading}
                  />
                </div>
                <button 
                  type="submit" 
                  disabled={isLoading || !topic.trim()} 
                  className="btn btn-primary"
                >
                  {isLoading ? (
                    <>
                      <span className="spinner"></span>
                      Generating...
                    </>
                  ) : (
                    <>
                      Explore Knowledge 🚀
                    </>
                  )}
                </button>
              </form>
            )}
          </div>
        )}

        {/* Tree View */}
        {currentView === 'tree' && tree && (
          <div className="tree-container">
            <div className="tree-header">
              <h1>{tree.tema}</h1>
              <p>Select a branch to explore in detail</p>
              <button onClick={goBack} className="btn btn-secondary" style={{marginTop: '1rem'}}>
                ← Back to Input
              </button>
            </div>

            {isLoading ? (
              <div className="loading">
                <span className="spinner"></span>
                <span>Loading...</span>
              </div>
            ) : (
              <div className="branches-grid">
                {tree.ramuri.map((branch, index) => (
                  <div
                    key={index}
                    className={`branch-item ${selectedBranch === branch ? 'selected' : ''}`}
                    onClick={() => handleBranchSelect(branch)}
                  >
                    <div className="branch-emoji">{branch.emoji}</div>
                    <h3 className="branch-name">{branch.nume}</h3>
                    <p className="branch-description">{branch.descriere}</p>
                    {branch.subcategorii && (
                      <div style={{fontSize: '0.875rem', color: '#475569', marginTop: '0.5rem'}}>
                        Topics: {branch.subcategorii.slice(0, 3).join(', ')}
                        {branch.subcategorii.length > 3 && '...'}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Article View */}
        {currentView === 'article' && article && (
          <div className="tree-container">
            <div className="card">
              <button onClick={goBack} className="btn btn-secondary" style={{marginBottom: '2rem'}}>
                ← Back to Tree
              </button>
              
              <h1 className="title">{article.title}</h1>
              <p style={{color: '#475569', marginBottom: '2rem'}}>
                Part of: {article.topic}
              </p>
              
              <div style={{lineHeight: '1.8', fontSize: '1.1rem'}}>
                {article.content.split('\n').map((paragraph, index) => (
                  paragraph.trim() && (
                    <p key={index} style={{marginBottom: '1rem'}}>
                      {paragraph}
                    </p>
                  )
                ))}
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default OptimizedApp;
