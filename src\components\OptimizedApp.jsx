import React, { useState, useEffect, useRef } from 'react';
import '../styles/optimized.css';
import gestureService, { createFlagWheel } from '../services/gestureService';
import speechService from '../services/speechService';
import exportService from '../services/exportService';
import gamificationService from '../services/optimizedGamificationService';

// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation
// Focus on core functionality with maximum impact

const OptimizedApp = () => {
  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'
  const [topic, setTopic] = useState('');
  const [tree, setTree] = useState(null);
  const [selectedBranch, setSelectedBranch] = useState(null);
  const [article, setArticle] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [user, setUser] = useState(null);
  const appRef = useRef(null);

  // Available flags for the optimized version
  const availableFlags = [
    { code: '-a', name: 'Article', description: 'Comprehensive article format' },
    { code: '-ex', name: 'Examples', description: 'Include practical examples' },
    { code: '-q', name: 'Quiz', description: 'Interactive quiz questions' },
    { code: '-vis', name: 'Visual', description: 'Include diagrams and visualizations' },
    { code: '-path', name: 'Learning Path', description: 'Structured learning progression' },
    { code: '-case', name: 'Case Study', description: 'Real-world case studies' },
    { code: '-ro', name: 'Romanian', description: 'Adapted for Romanian context' }
  ];

  // Initialize services and authentication
  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    const bypassSecurity = localStorage.getItem('bypassSecurity');

    if (storedUser || bypassSecurity) {
      const userData = {
        id: 'user-1',
        name: 'User',
        subscriptionTier: 'premium'
      };
      setUser(userData);

      // Award daily login points
      const result = gamificationService.awardPoints('DAILY_LOGIN');
      if (result.newAchievements.length > 0) {
        result.newAchievements.forEach(achievement => {
          gamificationService.showAchievementNotification(achievement);
        });
      }
    }

    // Initialize gesture service
    if (appRef.current) {
      gestureService.init(appRef.current, {
        doubleTap: handleDoubleTap,
        singleTap: handleSingleTap,
        longPress: handleLongPress
      });
    }

    return () => {
      gestureService.destroy();
    };
  }, [handleDoubleTap, handleSingleTap, handleLongPress]);

  // Initialize gamification UI when user is logged in
  useEffect(() => {
    if (user) {
      const container = document.getElementById('gamification-container');
      if (container) {
        // Clear existing content
        container.innerHTML = '';
        // Create gamification UI
        gamificationService.createGamificationUI(container);
      }
    }
  }, [user]);

  // Gesture handlers
  const handleDoubleTap = React.useCallback((event, targetInfo) => {
    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {
      // Show flag wheel on double tap of branch
      const branch = tree.ramuri[targetInfo.branchData.index];
      if (branch) {
        createFlagWheel(
          targetInfo.position,
          availableFlags,
          (selectedFlags) => {
            console.log('Selected flags:', selectedFlags);
          },
          (selectedFlags) => {
            generateArticle(branch, selectedFlags);
          }
        );
      }
    }
  }, [tree, availableFlags, generateArticle]);

  const handleSingleTap = React.useCallback((event, targetInfo) => {
    // Single tap for normal selection
    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {
      const branch = tree.ramuri[targetInfo.branchData.index];
      if (branch) {
        setSelectedBranch(branch);
      }
    }
  }, [tree]);

  const handleLongPress = React.useCallback((event, targetInfo) => {
    // Long press for quick article generation with default flags
    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {
      const branch = tree.ramuri[targetInfo.branchData.index];
      if (branch) {
        generateArticle(branch, ['-a']);
      }
    }
  }, [tree, generateArticle]);

  // Core API call - simplified
  const generateKnowledgeTree = async (topicInput) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Knowledge Tree Explorer'
        },
        body: JSON.stringify({
          model: 'deepseek/deepseek-r1-0528:free',
          messages: [{
            role: 'user',
            content: `Create a knowledge tree for "${topicInput}". Return JSON with:
            {
              "tema": "${topicInput}",
              "ramuri": [
                {
                  "nume": "Branch Name",
                  "descriere": "Brief description",
                  "emoji": "📚",
                  "subcategorii": ["Sub1", "Sub2", "Sub3"]
                }
              ]
            }
            Provide 6-8 main branches. Keep descriptions concise.`
          }],
          temperature: 0.7,
          max_tokens: 2000
        })
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }

      const data = await response.json();
      const content = data.choices[0].message.content;
      
      // Extract JSON from response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const treeData = JSON.parse(jsonMatch[0]);
        setTree(treeData);
        setCurrentView('tree');

        // Award points for tree generation
        const result = gamificationService.awardPoints('TREE_GENERATED');
        if (result.newAchievements.length > 0) {
          result.newAchievements.forEach(achievement => {
            gamificationService.showAchievementNotification(achievement);
          });
        }
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      console.error('Error generating tree:', err);
      setError('Failed to generate knowledge tree. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Generate article for selected branch with flags
  const generateArticle = React.useCallback(async (branch, flags = ['-a']) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Knowledge Tree Explorer'
        },
        body: JSON.stringify({
          model: 'deepseek/deepseek-r1-0528:free',
          messages: [{
            role: 'user',
            content: `Write a comprehensive article about "${branch.nume}" in the context of "${tree.tema}".

            Apply these flags: ${flags.join(', ')}

            Flag meanings:
            - "-a": Standard comprehensive article format
            - "-ex": Include 3 practical examples
            - "-q": Add 5 interactive quiz questions at the end
            - "-vis": Describe visual elements and diagrams
            - "-path": Structure as a learning path with steps
            - "-case": Include real-world case studies
            - "-ro": Adapt content for Romanian context and examples

            Make it educational and engaging. Length: 800-1200 words.`
          }],
          temperature: 0.7,
          max_tokens: 3000
        })
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }

      const data = await response.json();
      const content = data.choices[0].message.content;
      
      const articleData = {
        title: branch.nume,
        content: content,
        topic: tree.tema,
        flags: flags
      };

      setArticle(articleData);
      setCurrentView('article');

      // Award points for article generation
      const result = gamificationService.awardPoints('ARTICLE_GENERATED');
      if (result.newAchievements.length > 0) {
        result.newAchievements.forEach(achievement => {
          gamificationService.showAchievementNotification(achievement);
        });
      }
    } catch (err) {
      console.error('Error generating article:', err);
      setError('Failed to generate article. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [tree]);

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    if (topic.trim()) {
      generateKnowledgeTree(topic.trim());
    }
  };

  // Handle branch selection (single tap)
  const handleBranchSelect = (branch) => {
    setSelectedBranch(branch);
    // Don't auto-generate article, wait for double-tap or explicit action
  };

  // Speech functions
  const handleSpeechToggle = () => {
    if (!article) return;

    if (speechService.getStatus().isPlaying) {
      speechService.toggle();
    } else {
      speechService.speak(article.content);
      // Award points for using speech
      const result = gamificationService.awardPoints('SPEECH_USED');
      if (result.newAchievements.length > 0) {
        result.newAchievements.forEach(achievement => {
          gamificationService.showAchievementNotification(achievement);
        });
      }
    }
  };

  const handleSpeechStop = () => {
    speechService.stop();
  };

  const handleSpeechRateChange = (rate) => {
    speechService.setRate(rate);
  };

  // Export functions
  const handleExportPDF = () => {
    if (!article) return;
    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);
    if (result.success) {
      // Award points for export
      const gamResult = gamificationService.awardPoints('EXPORT_USED');
      if (gamResult.newAchievements.length > 0) {
        gamResult.newAchievements.forEach(achievement => {
          gamificationService.showAchievementNotification(achievement);
        });
      }
    }
  };

  const handleExportWord = () => {
    if (!article) return;
    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);
    if (result.success) {
      // Award points for export
      const gamResult = gamificationService.awardPoints('EXPORT_USED');
      if (gamResult.newAchievements.length > 0) {
        gamResult.newAchievements.forEach(achievement => {
          gamificationService.showAchievementNotification(achievement);
        });
      }
    }
  };

  const handleCopyToClipboard = async () => {
    if (!article) return;
    const result = await exportService.copyToClipboard(article.content);
    exportService.showMessage(result.message, result.success ? 'success' : 'error');
    if (result.success) {
      // Award points for export
      const gamResult = gamificationService.awardPoints('EXPORT_USED');
      if (gamResult.newAchievements.length > 0) {
        gamResult.newAchievements.forEach(achievement => {
          gamificationService.showAchievementNotification(achievement);
        });
      }
    }
  };

  // Navigation functions
  const goBack = () => {
    if (currentView === 'article') {
      setCurrentView('tree');
      setArticle(null);
    } else if (currentView === 'tree') {
      setCurrentView('input');
      setTree(null);
      setSelectedBranch(null);
    }
  };

  const goHome = () => {
    setCurrentView('input');
    setTree(null);
    setSelectedBranch(null);
    setArticle(null);
    setTopic('');
  };

  // Quick login for development
  const quickLogin = () => {
    localStorage.setItem('bypassSecurity', 'true');
    setUser({ id: 'dev-1', name: 'Developer', subscriptionTier: 'premium' });
  };

  return (
    <div className="app" ref={appRef}>
      {/* Header */}
      <header className="app-header">
        <div className="header-content">
          <button onClick={goHome} className="logo-text">
            🌳 Knowledge Tree Explorer
          </button>
          <div className="header-right">
            {user && (
              <div id="gamification-container" style={{ marginRight: '16px' }}>
                {/* Gamification UI will be inserted here */}
              </div>
            )}
            {!user ? (
              <button onClick={quickLogin} className="btn btn-primary">
                🚀 Quick Login
              </button>
            ) : (
              <span>Welcome, {user.name}!</span>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="main-content">
        {error && (
          <div className="error">
            ⚠️ {error}
            <button onClick={() => setError(null)} style={{marginLeft: 'auto', background: 'none', border: 'none', color: 'white', cursor: 'pointer'}}>
              ✕
            </button>
          </div>
        )}

        {/* Topic Input View */}
        {currentView === 'input' && (
          <div className="card text-center">
            <h1 className="title">Knowledge Tree Explorer</h1>
            <p className="subtitle">
              Enter any topic to generate an interactive knowledge tree with AI-powered content.
            </p>
            
            {!user ? (
              <div style={{background: '#f1f5f9', padding: '1rem', borderRadius: '0.5rem', marginBottom: '2rem'}}>
                <p style={{color: '#334155', marginBottom: '1rem'}}>
                  Please log in to access the knowledge tree generator.
                </p>
                <button onClick={quickLogin} className="btn btn-primary">
                  🚀 Quick Login (Development)
                </button>
              </div>
            ) : (
              <form onSubmit={handleSubmit}>
                <div className="form-group">
                  <input
                    type="text"
                    value={topic}
                    onChange={(e) => setTopic(e.target.value)}
                    placeholder="e.g., Quantum Physics, Machine Learning, History of Art..."
                    className="form-input"
                    disabled={isLoading}
                  />
                </div>
                <button 
                  type="submit" 
                  disabled={isLoading || !topic.trim()} 
                  className="btn btn-primary"
                >
                  {isLoading ? (
                    <>
                      <span className="spinner"></span>
                      Generating...
                    </>
                  ) : (
                    <>
                      Explore Knowledge 🚀
                    </>
                  )}
                </button>
              </form>
            )}
          </div>
        )}

        {/* Tree View */}
        {currentView === 'tree' && tree && (
          <div className="tree-container">
            <div className="tree-header">
              <h1>{tree.tema}</h1>
              <p>Select a branch to explore in detail</p>
              <button onClick={goBack} className="btn btn-secondary" style={{marginTop: '1rem'}}>
                ← Back to Input
              </button>
            </div>

            {isLoading ? (
              <div className="loading">
                <span className="spinner"></span>
                <span>Loading...</span>
              </div>
            ) : (
              <div className="branches-grid">
                {tree.ramuri.map((branch, index) => (
                  <div
                    key={index}
                    className={`branch-item ${selectedBranch === branch ? 'selected' : ''}`}
                    data-index={index}
                    data-name={branch.nume}
                    data-description={branch.descriere}
                    onClick={() => handleBranchSelect(branch)}
                  >
                    <div className="branch-emoji">{branch.emoji}</div>
                    <h3 className="branch-name">{branch.nume}</h3>
                    <p className="branch-description">{branch.descriere}</p>
                    {branch.subcategorii && (
                      <div style={{fontSize: '0.875rem', color: '#475569', marginTop: '0.5rem'}}>
                        Topics: {branch.subcategorii.slice(0, 3).join(', ')}
                        {branch.subcategorii.length > 3 && '...'}
                      </div>
                    )}
                    <div className="gesture-hint" style={{
                      fontSize: '0.75rem',
                      color: '#64748b',
                      marginTop: '0.5rem',
                      fontStyle: 'italic'
                    }}>
                      💡 Double-tap for flags • Long-press for quick article
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Article View */}
        {currentView === 'article' && article && (
          <div className="tree-container">
            <div className="card">
              <div className="article-header" style={{marginBottom: '2rem'}}>
                <button onClick={goBack} className="btn btn-secondary">
                  ← Back to Tree
                </button>

                {/* Article Controls */}
                <div className="article-controls" style={{
                  display: 'flex',
                  gap: '8px',
                  marginTop: '1rem',
                  flexWrap: 'wrap'
                }}>
                  {/* Speech Controls */}
                  <div className="speech-controls-compact" style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    padding: '8px 12px',
                    background: '#f1f5f9',
                    borderRadius: '6px',
                    border: '1px solid #e2e8f0'
                  }}>
                    <button
                      onClick={handleSpeechToggle}
                      className="btn-icon"
                      title="Play/Pause Speech"
                      style={{
                        background: 'none',
                        border: 'none',
                        fontSize: '16px',
                        cursor: 'pointer',
                        padding: '4px'
                      }}
                    >
                      {speechService.getStatus().isPlaying ? '⏸️' : '▶️'}
                    </button>
                    <button
                      onClick={handleSpeechStop}
                      className="btn-icon"
                      title="Stop Speech"
                      style={{
                        background: 'none',
                        border: 'none',
                        fontSize: '16px',
                        cursor: 'pointer',
                        padding: '4px'
                      }}
                    >
                      ⏹️
                    </button>
                    <input
                      type="range"
                      min="0.5"
                      max="2"
                      step="0.1"
                      defaultValue="1"
                      onChange={(e) => handleSpeechRateChange(parseFloat(e.target.value))}
                      style={{width: '60px'}}
                      title="Speech Speed"
                    />
                    <span style={{fontSize: '12px', color: '#64748b'}}>🗣️</span>
                  </div>

                  {/* Export Controls */}
                  <div className="export-controls-compact" style={{
                    display: 'flex',
                    gap: '4px'
                  }}>
                    <button
                      onClick={handleCopyToClipboard}
                      className="btn btn-secondary"
                      style={{padding: '6px 12px', fontSize: '12px'}}
                      title="Copy to Clipboard"
                    >
                      📋 Copy
                    </button>
                    <button
                      onClick={handleExportPDF}
                      className="btn btn-secondary"
                      style={{padding: '6px 12px', fontSize: '12px'}}
                      title="Export as PDF"
                    >
                      📄 PDF
                    </button>
                    <button
                      onClick={handleExportWord}
                      className="btn btn-secondary"
                      style={{padding: '6px 12px', fontSize: '12px'}}
                      title="Export as Word"
                    >
                      📝 Word
                    </button>
                  </div>
                </div>
              </div>

              <h1 className="title">{article.title}</h1>
              <div style={{color: '#475569', marginBottom: '2rem', fontSize: '0.9rem'}}>
                <span>Part of: {article.topic}</span>
                {article.flags && article.flags.length > 0 && (
                  <span style={{marginLeft: '16px'}}>
                    Flags: {article.flags.join(', ')}
                  </span>
                )}
              </div>

              <div className="article-content" style={{lineHeight: '1.8', fontSize: '1.1rem'}}>
                {article.content.split('\n').map((paragraph, index) => (
                  paragraph.trim() && (
                    <p key={index} style={{marginBottom: '1rem'}}>
                      {paragraph}
                    </p>
                  )
                ))}
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default OptimizedApp;
