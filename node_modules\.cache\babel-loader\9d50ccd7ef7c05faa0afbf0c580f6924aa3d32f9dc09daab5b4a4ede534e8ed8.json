{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Appv1\\\\src\\\\components\\\\OptimizedApp.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst OptimizedApp = () => {\n  _s();\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [tree, setTree] = useState(null);\n  const [selectedBranch, setSelectedBranch] = useState(null);\n  const [article, setArticle] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Available flags for the optimized version\n  const availableFlags = [{\n    code: '-a',\n    name: 'Article',\n    description: 'Comprehensive article format'\n  }, {\n    code: '-ex',\n    name: 'Examples',\n    description: 'Include practical examples'\n  }, {\n    code: '-q',\n    name: 'Quiz',\n    description: 'Interactive quiz questions'\n  }, {\n    code: '-vis',\n    name: 'Visual',\n    description: 'Include diagrams and visualizations'\n  }, {\n    code: '-path',\n    name: 'Learning Path',\n    description: 'Structured learning progression'\n  }, {\n    code: '-case',\n    name: 'Case Study',\n    description: 'Real-world case studies'\n  }, {\n    code: '-ro',\n    name: 'Romanian',\n    description: 'Adapted for Romanian context'\n  }];\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n    return () => {\n      gestureService.destroy();\n    };\n  }, []);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n    }\n  }, [user]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(targetInfo.position, availableFlags, selectedFlags => {\n          console.log('Selected flags:', selectedFlags);\n        }, selectedFlags => {\n          generateArticle(branch, selectedFlags);\n        });\n      }\n    }\n  }, [tree, availableFlags]);\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        setSelectedBranch(branch);\n      }\n    }\n  }, [tree]);\n  const handleLongPress = React.useCallback((event, targetInfo) => {\n    // Long press for quick article generation with default flags\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        generateArticle(branch, ['-a']);\n      }\n    }\n  }, [tree]);\n\n  // Core API call - simplified\n  const generateKnowledgeTree = async topicInput => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Create a knowledge tree for \"${topicInput}\". Return JSON with:\n            {\n              \"tema\": \"${topicInput}\",\n              \"ramuri\": [\n                {\n                  \"nume\": \"Branch Name\",\n                  \"descriere\": \"Brief description\",\n                  \"emoji\": \"📚\",\n                  \"subcategorii\": [\"Sub1\", \"Sub2\", \"Sub3\"]\n                }\n              ]\n            }\n            Provide 6-8 main branches. Keep descriptions concise.`\n          }],\n          temperature: 0.7,\n          max_tokens: 2000\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n\n      // Extract JSON from response\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        const treeData = JSON.parse(jsonMatch[0]);\n        setTree(treeData);\n        setCurrentView('tree');\n\n        // Award points for tree generation\n        const result = gamificationService.awardPoints('TREE_GENERATED');\n        if (result.newAchievements.length > 0) {\n          result.newAchievements.forEach(achievement => {\n            gamificationService.showAchievementNotification(achievement);\n          });\n        }\n      } else {\n        throw new Error('Invalid response format');\n      }\n    } catch (err) {\n      console.error('Error generating tree:', err);\n      setError('Failed to generate knowledge tree. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Generate article for selected branch with flags\n  const generateArticle = async (branch, flags = ['-a']) => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Write a comprehensive article about \"${branch.nume}\" in the context of \"${tree.tema}\".\n\n            Apply these flags: ${flags.join(', ')}\n\n            Flag meanings:\n            - \"-a\": Standard comprehensive article format\n            - \"-ex\": Include 3 practical examples\n            - \"-q\": Add 5 interactive quiz questions at the end\n            - \"-vis\": Describe visual elements and diagrams\n            - \"-path\": Structure as a learning path with steps\n            - \"-case\": Include real-world case studies\n            - \"-ro\": Adapt content for Romanian context and examples\n\n            Make it educational and engaging. Length: 800-1200 words.`\n          }],\n          temperature: 0.7,\n          max_tokens: 3000\n        })\n      });\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n      const articleData = {\n        title: branch.nume,\n        content: content,\n        topic: tree.tema,\n        flags: flags\n      };\n      setArticle(articleData);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('Error generating article:', err);\n      setError('Failed to generate article. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle form submission\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n    }\n  };\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = branch => {\n    setSelectedBranch(branch);\n    // Don't auto-generate article, wait for double-tap or explicit action\n  };\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article) return;\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n  const handleSpeechRateChange = rate => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleExportWord = () => {\n    if (!article) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n  const handleCopyToClipboard = async () => {\n    if (!article) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      setArticle(null);\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n      setTree(null);\n      setSelectedBranch(null);\n    }\n  };\n  const goHome = () => {\n    setCurrentView('input');\n    setTree(null);\n    setSelectedBranch(null);\n    setArticle(null);\n    setTopic('');\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({\n      id: 'dev-1',\n      name: 'Developer',\n      subscriptionTier: 'premium'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    ref: appRef,\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"app-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: goHome,\n          className: \"logo-text\",\n          children: \"\\uD83C\\uDF33 Knowledge Tree Explorer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-right\",\n          children: [user && /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"gamification-container\",\n            style: {\n              marginRight: '16px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 15\n          }, this), !user ? /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            children: \"\\uD83D\\uDE80 Quick Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Welcome, \", user.name, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-content\",\n      children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: [\"\\u26A0\\uFE0F \", error, /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setError(null),\n          style: {\n            marginLeft: 'auto',\n            background: 'none',\n            border: 'none',\n            color: 'white',\n            cursor: 'pointer'\n          },\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 11\n      }, this), currentView === 'input' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: \"Knowledge Tree Explorer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"subtitle\",\n          children: \"Enter any topic to generate an interactive knowledge tree with AI-powered content.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 13\n        }, this), !user ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f1f5f9',\n            padding: '1rem',\n            borderRadius: '0.5rem',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              color: '#334155',\n              marginBottom: '1rem'\n            },\n            children: \"Please log in to access the knowledge tree generator.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: quickLogin,\n            className: \"btn btn-primary\",\n            children: \"\\uD83D\\uDE80 Quick Login (Development)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: topic,\n              onChange: e => setTopic(e.target.value),\n              placeholder: \"e.g., Quantum Physics, Machine Learning, History of Art...\",\n              className: \"form-input\",\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isLoading || !topic.trim(),\n            className: \"btn btn-primary\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 23\n              }, this), \"Generating...\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: \"Explore Knowledge \\uD83D\\uDE80\"\n            }, void 0, false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 11\n      }, this), currentView === 'tree' && tree && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tree-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: tree.tema\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Select a branch to explore in detail\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: goBack,\n            className: \"btn btn-secondary\",\n            style: {\n              marginTop: '1rem'\n            },\n            children: \"\\u2190 Back to Input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 13\n        }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"branches-grid\",\n          children: tree.ramuri.map((branch, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `branch-item ${selectedBranch === branch ? 'selected' : ''}`,\n            \"data-index\": index,\n            \"data-name\": branch.nume,\n            \"data-description\": branch.descriere,\n            onClick: () => handleBranchSelect(branch),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"branch-emoji\",\n              children: branch.emoji\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"branch-name\",\n              children: branch.nume\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"branch-description\",\n              children: branch.descriere\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 21\n            }, this), branch.subcategorii && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.875rem',\n                color: '#475569',\n                marginTop: '0.5rem'\n              },\n              children: [\"Topics: \", branch.subcategorii.slice(0, 3).join(', '), branch.subcategorii.length > 3 && '...']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"gesture-hint\",\n              style: {\n                fontSize: '0.75rem',\n                color: '#64748b',\n                marginTop: '0.5rem',\n                fontStyle: 'italic'\n              },\n              children: \"\\uD83D\\uDCA1 Double-tap for flags \\u2022 Long-press for quick article\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 21\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 11\n      }, this), currentView === 'article' && article && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tree-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-header\",\n            style: {\n              marginBottom: '2rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: goBack,\n              className: \"btn btn-secondary\",\n              children: \"\\u2190 Back to Tree\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"article-controls\",\n              style: {\n                display: 'flex',\n                gap: '8px',\n                marginTop: '1rem',\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"speech-controls-compact\",\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '8px',\n                  padding: '8px 12px',\n                  background: '#f1f5f9',\n                  borderRadius: '6px',\n                  border: '1px solid #e2e8f0'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechToggle,\n                  className: \"btn-icon\",\n                  title: \"Play/Pause Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: speechService.getStatus().isPlaying ? '⏸️' : '▶️'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSpeechStop,\n                  className: \"btn-icon\",\n                  title: \"Stop Speech\",\n                  style: {\n                    background: 'none',\n                    border: 'none',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    padding: '4px'\n                  },\n                  children: \"\\u23F9\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"range\",\n                  min: \"0.5\",\n                  max: \"2\",\n                  step: \"0.1\",\n                  defaultValue: \"1\",\n                  onChange: e => handleSpeechRateChange(parseFloat(e.target.value)),\n                  style: {\n                    width: '60px'\n                  },\n                  title: \"Speech Speed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '12px',\n                    color: '#64748b'\n                  },\n                  children: \"\\uD83D\\uDDE3\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"export-controls-compact\",\n                style: {\n                  display: 'flex',\n                  gap: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleCopyToClipboard,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Copy to Clipboard\",\n                  children: \"\\uD83D\\uDCCB Copy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportPDF,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as PDF\",\n                  children: \"\\uD83D\\uDCC4 PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleExportWord,\n                  className: \"btn btn-secondary\",\n                  style: {\n                    padding: '6px 12px',\n                    fontSize: '12px'\n                  },\n                  title: \"Export as Word\",\n                  children: \"\\uD83D\\uDCDD Word\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"title\",\n            children: article.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#475569',\n              marginBottom: '2rem',\n              fontSize: '0.9rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Part of: \", article.topic]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 17\n            }, this), article.flags && article.flags.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                marginLeft: '16px'\n              },\n              children: [\"Flags: \", article.flags.join(', ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"article-content\",\n            style: {\n              lineHeight: '1.8',\n              fontSize: '1.1rem'\n            },\n            children: article.content.split('\\n').map((paragraph, index) => paragraph.trim() && /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: paragraph\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 372,\n    columnNumber: 5\n  }, this);\n};\n_s(OptimizedApp, \"EvW3UQ7jTIKYM75nZQrZmqdTWt4=\");\n_c = OptimizedApp;\nexport default OptimizedApp;\nvar _c;\n$RefreshReg$(_c, \"OptimizedApp\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "gestureService", "createFlagWheel", "speechService", "exportService", "gamificationService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "OptimizedApp", "_s", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "topic", "setTopic", "tree", "setTree", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedBranch", "article", "setArticle", "isLoading", "setIsLoading", "error", "setError", "user", "setUser", "appRef", "availableFlags", "code", "name", "description", "storedUser", "localStorage", "getItem", "bypassSecurity", "userData", "id", "subscriptionTier", "result", "awardPoints", "newAchievements", "length", "for<PERSON>ach", "achievement", "showAchievementNotification", "current", "init", "doubleTap", "handleDoubleTap", "singleTap", "handleSingleTap", "longPress", "handleLongPress", "destroy", "container", "document", "getElementById", "innerHTML", "createGamificationUI", "useCallback", "event", "targetInfo", "isBranchItem", "branchData", "branch", "<PERSON><PERSON>", "index", "position", "selected<PERSON><PERSON><PERSON>", "console", "log", "generateArticle", "generateKnowledgeTree", "topicInput", "response", "fetch", "method", "headers", "process", "env", "REACT_APP_OPENROUTER_API_KEY", "window", "location", "origin", "body", "JSON", "stringify", "model", "messages", "role", "content", "temperature", "max_tokens", "ok", "Error", "status", "data", "json", "choices", "message", "jsonMatch", "match", "treeData", "parse", "err", "flags", "nume", "tema", "join", "articleData", "title", "handleSubmit", "e", "preventDefault", "trim", "handleBranchSelect", "handleSpeechToggle", "getStatus", "isPlaying", "toggle", "speak", "handleSpeechStop", "stop", "handleSpeechRateChange", "rate", "setRate", "handleExportPDF", "exportAsPDF", "replace", "success", "gamResult", "handleExportWord", "exportAsWord", "handleCopyToClipboard", "copyToClipboard", "showMessage", "goBack", "goHome", "quickLogin", "setItem", "className", "ref", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginRight", "marginLeft", "background", "border", "color", "cursor", "padding", "borderRadius", "marginBottom", "onSubmit", "type", "value", "onChange", "target", "placeholder", "disabled", "marginTop", "map", "desc<PERSON><PERSON>", "emoji", "subcategorii", "fontSize", "slice", "fontStyle", "display", "gap", "flexWrap", "alignItems", "min", "max", "step", "defaultValue", "parseFloat", "width", "lineHeight", "split", "paragraph", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/augment-projects/Appv1/src/components/OptimizedApp.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport '../styles/optimized.css';\nimport gestureService, { createFlagWheel } from '../services/gestureService';\nimport speechService from '../services/speechService';\nimport exportService from '../services/exportService';\nimport gamificationService from '../services/optimizedGamificationService';\n\n// Optimized Knowledge Tree Explorer - Pareto 80/20 Implementation\n// Focus on core functionality with maximum impact\n\nconst OptimizedApp = () => {\n  const [currentView, setCurrentView] = useState('input'); // 'input', 'tree', 'article'\n  const [topic, setTopic] = useState('');\n  const [tree, setTree] = useState(null);\n  const [selectedBranch, setSelectedBranch] = useState(null);\n  const [article, setArticle] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [user, setUser] = useState(null);\n  const appRef = useRef(null);\n\n  // Available flags for the optimized version\n  const availableFlags = [\n    { code: '-a', name: 'Article', description: 'Comprehensive article format' },\n    { code: '-ex', name: 'Examples', description: 'Include practical examples' },\n    { code: '-q', name: 'Quiz', description: 'Interactive quiz questions' },\n    { code: '-vis', name: 'Visual', description: 'Include diagrams and visualizations' },\n    { code: '-path', name: 'Learning Path', description: 'Structured learning progression' },\n    { code: '-case', name: 'Case Study', description: 'Real-world case studies' },\n    { code: '-ro', name: 'Romanian', description: 'Adapted for Romanian context' }\n  ];\n\n  // Initialize services and authentication\n  useEffect(() => {\n    const storedUser = localStorage.getItem('user');\n    const bypassSecurity = localStorage.getItem('bypassSecurity');\n\n    if (storedUser || bypassSecurity) {\n      const userData = {\n        id: 'user-1',\n        name: 'User',\n        subscriptionTier: 'premium'\n      };\n      setUser(userData);\n\n      // Award daily login points\n      const result = gamificationService.awardPoints('DAILY_LOGIN');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n\n    // Initialize gesture service\n    if (appRef.current) {\n      gestureService.init(appRef.current, {\n        doubleTap: handleDoubleTap,\n        singleTap: handleSingleTap,\n        longPress: handleLongPress\n      });\n    }\n\n    return () => {\n      gestureService.destroy();\n    };\n  }, []);\n\n  // Initialize gamification UI when user is logged in\n  useEffect(() => {\n    if (user) {\n      const container = document.getElementById('gamification-container');\n      if (container) {\n        // Clear existing content\n        container.innerHTML = '';\n        // Create gamification UI\n        gamificationService.createGamificationUI(container);\n      }\n    }\n  }, [user]);\n\n  // Gesture handlers\n  const handleDoubleTap = React.useCallback((event, targetInfo) => {\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      // Show flag wheel on double tap of branch\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        createFlagWheel(\n          targetInfo.position,\n          availableFlags,\n          (selectedFlags) => {\n            console.log('Selected flags:', selectedFlags);\n          },\n          (selectedFlags) => {\n            generateArticle(branch, selectedFlags);\n          }\n        );\n      }\n    }\n  }, [tree, availableFlags]);\n\n  const handleSingleTap = React.useCallback((event, targetInfo) => {\n    // Single tap for normal selection\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        setSelectedBranch(branch);\n      }\n    }\n  }, [tree]);\n\n  const handleLongPress = React.useCallback((event, targetInfo) => {\n    // Long press for quick article generation with default flags\n    if (targetInfo.isBranchItem && targetInfo.branchData && tree) {\n      const branch = tree.ramuri[targetInfo.branchData.index];\n      if (branch) {\n        generateArticle(branch, ['-a']);\n      }\n    }\n  }, [tree]);\n\n  // Core API call - simplified\n  const generateKnowledgeTree = async (topicInput) => {\n    setIsLoading(true);\n    setError(null);\n    \n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Create a knowledge tree for \"${topicInput}\". Return JSON with:\n            {\n              \"tema\": \"${topicInput}\",\n              \"ramuri\": [\n                {\n                  \"nume\": \"Branch Name\",\n                  \"descriere\": \"Brief description\",\n                  \"emoji\": \"📚\",\n                  \"subcategorii\": [\"Sub1\", \"Sub2\", \"Sub3\"]\n                }\n              ]\n            }\n            Provide 6-8 main branches. Keep descriptions concise.`\n          }],\n          temperature: 0.7,\n          max_tokens: 2000\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n      \n      // Extract JSON from response\n      const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n      if (jsonMatch) {\n        const treeData = JSON.parse(jsonMatch[0]);\n        setTree(treeData);\n        setCurrentView('tree');\n\n        // Award points for tree generation\n        const result = gamificationService.awardPoints('TREE_GENERATED');\n        if (result.newAchievements.length > 0) {\n          result.newAchievements.forEach(achievement => {\n            gamificationService.showAchievementNotification(achievement);\n          });\n        }\n      } else {\n        throw new Error('Invalid response format');\n      }\n    } catch (err) {\n      console.error('Error generating tree:', err);\n      setError('Failed to generate knowledge tree. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Generate article for selected branch with flags\n  const generateArticle = async (branch, flags = ['-a']) => {\n    setIsLoading(true);\n    setError(null);\n    \n    try {\n      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${process.env.REACT_APP_OPENROUTER_API_KEY}`,\n          'Content-Type': 'application/json',\n          'HTTP-Referer': window.location.origin,\n          'X-Title': 'Knowledge Tree Explorer'\n        },\n        body: JSON.stringify({\n          model: 'deepseek/deepseek-r1-0528:free',\n          messages: [{\n            role: 'user',\n            content: `Write a comprehensive article about \"${branch.nume}\" in the context of \"${tree.tema}\".\n\n            Apply these flags: ${flags.join(', ')}\n\n            Flag meanings:\n            - \"-a\": Standard comprehensive article format\n            - \"-ex\": Include 3 practical examples\n            - \"-q\": Add 5 interactive quiz questions at the end\n            - \"-vis\": Describe visual elements and diagrams\n            - \"-path\": Structure as a learning path with steps\n            - \"-case\": Include real-world case studies\n            - \"-ro\": Adapt content for Romanian context and examples\n\n            Make it educational and engaging. Length: 800-1200 words.`\n          }],\n          temperature: 0.7,\n          max_tokens: 3000\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error(`API Error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      const content = data.choices[0].message.content;\n      \n      const articleData = {\n        title: branch.nume,\n        content: content,\n        topic: tree.tema,\n        flags: flags\n      };\n\n      setArticle(articleData);\n      setCurrentView('article');\n\n      // Award points for article generation\n      const result = gamificationService.awardPoints('ARTICLE_GENERATED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    } catch (err) {\n      console.error('Error generating article:', err);\n      setError('Failed to generate article. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle form submission\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (topic.trim()) {\n      generateKnowledgeTree(topic.trim());\n    }\n  };\n\n  // Handle branch selection (single tap)\n  const handleBranchSelect = (branch) => {\n    setSelectedBranch(branch);\n    // Don't auto-generate article, wait for double-tap or explicit action\n  };\n\n  // Speech functions\n  const handleSpeechToggle = () => {\n    if (!article) return;\n\n    if (speechService.getStatus().isPlaying) {\n      speechService.toggle();\n    } else {\n      speechService.speak(article.content);\n      // Award points for using speech\n      const result = gamificationService.awardPoints('SPEECH_USED');\n      if (result.newAchievements.length > 0) {\n        result.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleSpeechStop = () => {\n    speechService.stop();\n  };\n\n  const handleSpeechRateChange = (rate) => {\n    speechService.setRate(rate);\n  };\n\n  // Export functions\n  const handleExportPDF = () => {\n    if (!article) return;\n    const result = exportService.exportAsPDF(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleExportWord = () => {\n    if (!article) return;\n    const result = exportService.exportAsWord(article, `${article.title.replace(/[^a-zA-Z0-9]/g, '-')}`);\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  const handleCopyToClipboard = async () => {\n    if (!article) return;\n    const result = await exportService.copyToClipboard(article.content);\n    exportService.showMessage(result.message, result.success ? 'success' : 'error');\n    if (result.success) {\n      // Award points for export\n      const gamResult = gamificationService.awardPoints('EXPORT_USED');\n      if (gamResult.newAchievements.length > 0) {\n        gamResult.newAchievements.forEach(achievement => {\n          gamificationService.showAchievementNotification(achievement);\n        });\n      }\n    }\n  };\n\n  // Navigation functions\n  const goBack = () => {\n    if (currentView === 'article') {\n      setCurrentView('tree');\n      setArticle(null);\n    } else if (currentView === 'tree') {\n      setCurrentView('input');\n      setTree(null);\n      setSelectedBranch(null);\n    }\n  };\n\n  const goHome = () => {\n    setCurrentView('input');\n    setTree(null);\n    setSelectedBranch(null);\n    setArticle(null);\n    setTopic('');\n  };\n\n  // Quick login for development\n  const quickLogin = () => {\n    localStorage.setItem('bypassSecurity', 'true');\n    setUser({ id: 'dev-1', name: 'Developer', subscriptionTier: 'premium' });\n  };\n\n  return (\n    <div className=\"app\" ref={appRef}>\n      {/* Header */}\n      <header className=\"app-header\">\n        <div className=\"header-content\">\n          <button onClick={goHome} className=\"logo-text\">\n            🌳 Knowledge Tree Explorer\n          </button>\n          <div className=\"header-right\">\n            {user && (\n              <div id=\"gamification-container\" style={{ marginRight: '16px' }}>\n                {/* Gamification UI will be inserted here */}\n              </div>\n            )}\n            {!user ? (\n              <button onClick={quickLogin} className=\"btn btn-primary\">\n                🚀 Quick Login\n              </button>\n            ) : (\n              <span>Welcome, {user.name}!</span>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"main-content\">\n        {error && (\n          <div className=\"error\">\n            ⚠️ {error}\n            <button onClick={() => setError(null)} style={{marginLeft: 'auto', background: 'none', border: 'none', color: 'white', cursor: 'pointer'}}>\n              ✕\n            </button>\n          </div>\n        )}\n\n        {/* Topic Input View */}\n        {currentView === 'input' && (\n          <div className=\"card text-center\">\n            <h1 className=\"title\">Knowledge Tree Explorer</h1>\n            <p className=\"subtitle\">\n              Enter any topic to generate an interactive knowledge tree with AI-powered content.\n            </p>\n            \n            {!user ? (\n              <div style={{background: '#f1f5f9', padding: '1rem', borderRadius: '0.5rem', marginBottom: '2rem'}}>\n                <p style={{color: '#334155', marginBottom: '1rem'}}>\n                  Please log in to access the knowledge tree generator.\n                </p>\n                <button onClick={quickLogin} className=\"btn btn-primary\">\n                  🚀 Quick Login (Development)\n                </button>\n              </div>\n            ) : (\n              <form onSubmit={handleSubmit}>\n                <div className=\"form-group\">\n                  <input\n                    type=\"text\"\n                    value={topic}\n                    onChange={(e) => setTopic(e.target.value)}\n                    placeholder=\"e.g., Quantum Physics, Machine Learning, History of Art...\"\n                    className=\"form-input\"\n                    disabled={isLoading}\n                  />\n                </div>\n                <button \n                  type=\"submit\" \n                  disabled={isLoading || !topic.trim()} \n                  className=\"btn btn-primary\"\n                >\n                  {isLoading ? (\n                    <>\n                      <span className=\"spinner\"></span>\n                      Generating...\n                    </>\n                  ) : (\n                    <>\n                      Explore Knowledge 🚀\n                    </>\n                  )}\n                </button>\n              </form>\n            )}\n          </div>\n        )}\n\n        {/* Tree View */}\n        {currentView === 'tree' && tree && (\n          <div className=\"tree-container\">\n            <div className=\"tree-header\">\n              <h1>{tree.tema}</h1>\n              <p>Select a branch to explore in detail</p>\n              <button onClick={goBack} className=\"btn btn-secondary\" style={{marginTop: '1rem'}}>\n                ← Back to Input\n              </button>\n            </div>\n\n            {isLoading ? (\n              <div className=\"loading\">\n                <span className=\"spinner\"></span>\n                <span>Loading...</span>\n              </div>\n            ) : (\n              <div className=\"branches-grid\">\n                {tree.ramuri.map((branch, index) => (\n                  <div\n                    key={index}\n                    className={`branch-item ${selectedBranch === branch ? 'selected' : ''}`}\n                    data-index={index}\n                    data-name={branch.nume}\n                    data-description={branch.descriere}\n                    onClick={() => handleBranchSelect(branch)}\n                  >\n                    <div className=\"branch-emoji\">{branch.emoji}</div>\n                    <h3 className=\"branch-name\">{branch.nume}</h3>\n                    <p className=\"branch-description\">{branch.descriere}</p>\n                    {branch.subcategorii && (\n                      <div style={{fontSize: '0.875rem', color: '#475569', marginTop: '0.5rem'}}>\n                        Topics: {branch.subcategorii.slice(0, 3).join(', ')}\n                        {branch.subcategorii.length > 3 && '...'}\n                      </div>\n                    )}\n                    <div className=\"gesture-hint\" style={{\n                      fontSize: '0.75rem',\n                      color: '#64748b',\n                      marginTop: '0.5rem',\n                      fontStyle: 'italic'\n                    }}>\n                      💡 Double-tap for flags • Long-press for quick article\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Article View */}\n        {currentView === 'article' && article && (\n          <div className=\"tree-container\">\n            <div className=\"card\">\n              <div className=\"article-header\" style={{marginBottom: '2rem'}}>\n                <button onClick={goBack} className=\"btn btn-secondary\">\n                  ← Back to Tree\n                </button>\n\n                {/* Article Controls */}\n                <div className=\"article-controls\" style={{\n                  display: 'flex',\n                  gap: '8px',\n                  marginTop: '1rem',\n                  flexWrap: 'wrap'\n                }}>\n                  {/* Speech Controls */}\n                  <div className=\"speech-controls-compact\" style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    padding: '8px 12px',\n                    background: '#f1f5f9',\n                    borderRadius: '6px',\n                    border: '1px solid #e2e8f0'\n                  }}>\n                    <button\n                      onClick={handleSpeechToggle}\n                      className=\"btn-icon\"\n                      title=\"Play/Pause Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      {speechService.getStatus().isPlaying ? '⏸️' : '▶️'}\n                    </button>\n                    <button\n                      onClick={handleSpeechStop}\n                      className=\"btn-icon\"\n                      title=\"Stop Speech\"\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        fontSize: '16px',\n                        cursor: 'pointer',\n                        padding: '4px'\n                      }}\n                    >\n                      ⏹️\n                    </button>\n                    <input\n                      type=\"range\"\n                      min=\"0.5\"\n                      max=\"2\"\n                      step=\"0.1\"\n                      defaultValue=\"1\"\n                      onChange={(e) => handleSpeechRateChange(parseFloat(e.target.value))}\n                      style={{width: '60px'}}\n                      title=\"Speech Speed\"\n                    />\n                    <span style={{fontSize: '12px', color: '#64748b'}}>🗣️</span>\n                  </div>\n\n                  {/* Export Controls */}\n                  <div className=\"export-controls-compact\" style={{\n                    display: 'flex',\n                    gap: '4px'\n                  }}>\n                    <button\n                      onClick={handleCopyToClipboard}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Copy to Clipboard\"\n                    >\n                      📋 Copy\n                    </button>\n                    <button\n                      onClick={handleExportPDF}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as PDF\"\n                    >\n                      📄 PDF\n                    </button>\n                    <button\n                      onClick={handleExportWord}\n                      className=\"btn btn-secondary\"\n                      style={{padding: '6px 12px', fontSize: '12px'}}\n                      title=\"Export as Word\"\n                    >\n                      📝 Word\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <h1 className=\"title\">{article.title}</h1>\n              <div style={{color: '#475569', marginBottom: '2rem', fontSize: '0.9rem'}}>\n                <span>Part of: {article.topic}</span>\n                {article.flags && article.flags.length > 0 && (\n                  <span style={{marginLeft: '16px'}}>\n                    Flags: {article.flags.join(', ')}\n                  </span>\n                )}\n              </div>\n\n              <div className=\"article-content\" style={{lineHeight: '1.8', fontSize: '1.1rem'}}>\n                {article.content.split('\\n').map((paragraph, index) => (\n                  paragraph.trim() && (\n                    <p key={index} style={{marginBottom: '1rem'}}>\n                      {paragraph}\n                    </p>\n                  )\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n};\n\nexport default OptimizedApp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,yBAAyB;AAChC,OAAOC,cAAc,IAAIC,eAAe,QAAQ,4BAA4B;AAC5E,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,mBAAmB,MAAM,0CAA0C;;AAE1E;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACzD,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4B,IAAI,EAAEC,OAAO,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM8B,MAAM,GAAG5B,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAM6B,cAAc,GAAG,CACrB;IAAEC,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,SAAS;IAAEC,WAAW,EAAE;EAA+B,CAAC,EAC5E;IAAEF,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAE;EAA6B,CAAC,EAC5E;IAAEF,IAAI,EAAE,IAAI;IAAEC,IAAI,EAAE,MAAM;IAAEC,WAAW,EAAE;EAA6B,CAAC,EACvE;IAAEF,IAAI,EAAE,MAAM;IAAEC,IAAI,EAAE,QAAQ;IAAEC,WAAW,EAAE;EAAsC,CAAC,EACpF;IAAEF,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,eAAe;IAAEC,WAAW,EAAE;EAAkC,CAAC,EACxF;IAAEF,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,YAAY;IAAEC,WAAW,EAAE;EAA0B,CAAC,EAC7E;IAAEF,IAAI,EAAE,KAAK;IAAEC,IAAI,EAAE,UAAU;IAAEC,WAAW,EAAE;EAA+B,CAAC,CAC/E;;EAED;EACAjC,SAAS,CAAC,MAAM;IACd,MAAMkC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,MAAMC,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAE7D,IAAIF,UAAU,IAAIG,cAAc,EAAE;MAChC,MAAMC,QAAQ,GAAG;QACfC,EAAE,EAAE,QAAQ;QACZP,IAAI,EAAE,MAAM;QACZQ,gBAAgB,EAAE;MACpB,CAAC;MACDZ,OAAO,CAACU,QAAQ,CAAC;;MAEjB;MACA,MAAMG,MAAM,GAAGnC,mBAAmB,CAACoC,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CxC,mBAAmB,CAACyC,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAIjB,MAAM,CAACmB,OAAO,EAAE;MAClB9C,cAAc,CAAC+C,IAAI,CAACpB,MAAM,CAACmB,OAAO,EAAE;QAClCE,SAAS,EAAEC,eAAe;QAC1BC,SAAS,EAAEC,eAAe;QAC1BC,SAAS,EAAEC;MACb,CAAC,CAAC;IACJ;IAEA,OAAO,MAAM;MACXrD,cAAc,CAACsD,OAAO,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxD,SAAS,CAAC,MAAM;IACd,IAAI2B,IAAI,EAAE;MACR,MAAM8B,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,wBAAwB,CAAC;MACnE,IAAIF,SAAS,EAAE;QACb;QACAA,SAAS,CAACG,SAAS,GAAG,EAAE;QACxB;QACAtD,mBAAmB,CAACuD,oBAAoB,CAACJ,SAAS,CAAC;MACrD;IACF;EACF,CAAC,EAAE,CAAC9B,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMwB,eAAe,GAAGrD,KAAK,CAACgE,WAAW,CAAC,CAACC,KAAK,EAAEC,UAAU,KAAK;IAC/D,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAIjD,IAAI,EAAE;MAC5D;MACA,MAAMkD,MAAM,GAAGlD,IAAI,CAACmD,MAAM,CAACJ,UAAU,CAACE,UAAU,CAACG,KAAK,CAAC;MACvD,IAAIF,MAAM,EAAE;QACVhE,eAAe,CACb6D,UAAU,CAACM,QAAQ,EACnBxC,cAAc,EACbyC,aAAa,IAAK;UACjBC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,aAAa,CAAC;QAC/C,CAAC,EACAA,aAAa,IAAK;UACjBG,eAAe,CAACP,MAAM,EAAEI,aAAa,CAAC;QACxC,CACF,CAAC;MACH;IACF;EACF,CAAC,EAAE,CAACtD,IAAI,EAAEa,cAAc,CAAC,CAAC;EAE1B,MAAMuB,eAAe,GAAGvD,KAAK,CAACgE,WAAW,CAAC,CAACC,KAAK,EAAEC,UAAU,KAAK;IAC/D;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAIjD,IAAI,EAAE;MAC5D,MAAMkD,MAAM,GAAGlD,IAAI,CAACmD,MAAM,CAACJ,UAAU,CAACE,UAAU,CAACG,KAAK,CAAC;MACvD,IAAIF,MAAM,EAAE;QACV/C,iBAAiB,CAAC+C,MAAM,CAAC;MAC3B;IACF;EACF,CAAC,EAAE,CAAClD,IAAI,CAAC,CAAC;EAEV,MAAMsC,eAAe,GAAGzD,KAAK,CAACgE,WAAW,CAAC,CAACC,KAAK,EAAEC,UAAU,KAAK;IAC/D;IACA,IAAIA,UAAU,CAACC,YAAY,IAAID,UAAU,CAACE,UAAU,IAAIjD,IAAI,EAAE;MAC5D,MAAMkD,MAAM,GAAGlD,IAAI,CAACmD,MAAM,CAACJ,UAAU,CAACE,UAAU,CAACG,KAAK,CAAC;MACvD,IAAIF,MAAM,EAAE;QACVO,eAAe,CAACP,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC;MACjC;IACF;EACF,CAAC,EAAE,CAAClD,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM0D,qBAAqB,GAAG,MAAOC,UAAU,IAAK;IAClDpD,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMmD,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,OAAO,CAACC,GAAG,CAACC,4BAA4B,EAAE;UACrE,cAAc,EAAE,kBAAkB;UAClC,cAAc,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM;UACtC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,gCAAgC;UACvCC,QAAQ,EAAE,CAAC;YACTC,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,gCAAgCjB,UAAU;AAC/D;AACA,yBAAyBA,UAAU;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACU,CAAC,CAAC;UACFkB,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAAClB,QAAQ,CAACmB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,cAAcpB,QAAQ,CAACqB,MAAM,EAAE,CAAC;MAClD;MAEA,MAAMC,IAAI,GAAG,MAAMtB,QAAQ,CAACuB,IAAI,CAAC,CAAC;MAClC,MAAMP,OAAO,GAAGM,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAACT,OAAO;;MAE/C;MACA,MAAMU,SAAS,GAAGV,OAAO,CAACW,KAAK,CAAC,aAAa,CAAC;MAC9C,IAAID,SAAS,EAAE;QACb,MAAME,QAAQ,GAAGjB,IAAI,CAACkB,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;QACzCrF,OAAO,CAACuF,QAAQ,CAAC;QACjB3F,cAAc,CAAC,MAAM,CAAC;;QAEtB;QACA,MAAM2B,MAAM,GAAGnC,mBAAmB,CAACoC,WAAW,CAAC,gBAAgB,CAAC;QAChE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;UACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;YAC5CxC,mBAAmB,CAACyC,2BAA2B,CAACD,WAAW,CAAC;UAC9D,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,MAAM,IAAImD,KAAK,CAAC,yBAAyB,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZnC,OAAO,CAAC/C,KAAK,CAAC,wBAAwB,EAAEkF,GAAG,CAAC;MAC5CjF,QAAQ,CAAC,sDAAsD,CAAC;IAClE,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMkD,eAAe,GAAG,MAAAA,CAAOP,MAAM,EAAEyC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK;IACxDpF,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMmD,QAAQ,GAAG,MAAMC,KAAK,CAAC,+CAA+C,EAAE;QAC5EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,OAAO,CAACC,GAAG,CAACC,4BAA4B,EAAE;UACrE,cAAc,EAAE,kBAAkB;UAClC,cAAc,EAAEC,MAAM,CAACC,QAAQ,CAACC,MAAM;UACtC,SAAS,EAAE;QACb,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBC,KAAK,EAAE,gCAAgC;UACvCC,QAAQ,EAAE,CAAC;YACTC,IAAI,EAAE,MAAM;YACZC,OAAO,EAAE,wCAAwC1B,MAAM,CAAC0C,IAAI,wBAAwB5F,IAAI,CAAC6F,IAAI;AACzG;AACA,iCAAiCF,KAAK,CAACG,IAAI,CAAC,IAAI,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACU,CAAC,CAAC;UACFjB,WAAW,EAAE,GAAG;UAChBC,UAAU,EAAE;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAI,CAAClB,QAAQ,CAACmB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,cAAcpB,QAAQ,CAACqB,MAAM,EAAE,CAAC;MAClD;MAEA,MAAMC,IAAI,GAAG,MAAMtB,QAAQ,CAACuB,IAAI,CAAC,CAAC;MAClC,MAAMP,OAAO,GAAGM,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAACT,OAAO;MAE/C,MAAMmB,WAAW,GAAG;QAClBC,KAAK,EAAE9C,MAAM,CAAC0C,IAAI;QAClBhB,OAAO,EAAEA,OAAO;QAChB9E,KAAK,EAAEE,IAAI,CAAC6F,IAAI;QAChBF,KAAK,EAAEA;MACT,CAAC;MAEDtF,UAAU,CAAC0F,WAAW,CAAC;MACvBlG,cAAc,CAAC,SAAS,CAAC;;MAEzB;MACA,MAAM2B,MAAM,GAAGnC,mBAAmB,CAACoC,WAAW,CAAC,mBAAmB,CAAC;MACnE,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CxC,mBAAmB,CAACyC,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAO6D,GAAG,EAAE;MACZnC,OAAO,CAAC/C,KAAK,CAAC,2BAA2B,EAAEkF,GAAG,CAAC;MAC/CjF,QAAQ,CAAC,+CAA+C,CAAC;IAC3D,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM0F,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIrG,KAAK,CAACsG,IAAI,CAAC,CAAC,EAAE;MAChB1C,qBAAqB,CAAC5D,KAAK,CAACsG,IAAI,CAAC,CAAC,CAAC;IACrC;EACF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAInD,MAAM,IAAK;IACrC/C,iBAAiB,CAAC+C,MAAM,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMoD,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAAClG,OAAO,EAAE;IAEd,IAAIjB,aAAa,CAACoH,SAAS,CAAC,CAAC,CAACC,SAAS,EAAE;MACvCrH,aAAa,CAACsH,MAAM,CAAC,CAAC;IACxB,CAAC,MAAM;MACLtH,aAAa,CAACuH,KAAK,CAACtG,OAAO,CAACwE,OAAO,CAAC;MACpC;MACA,MAAMpD,MAAM,GAAGnC,mBAAmB,CAACoC,WAAW,CAAC,aAAa,CAAC;MAC7D,IAAID,MAAM,CAACE,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCH,MAAM,CAACE,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC5CxC,mBAAmB,CAACyC,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAM8E,gBAAgB,GAAGA,CAAA,KAAM;IAC7BxH,aAAa,CAACyH,IAAI,CAAC,CAAC;EACtB,CAAC;EAED,MAAMC,sBAAsB,GAAIC,IAAI,IAAK;IACvC3H,aAAa,CAAC4H,OAAO,CAACD,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAC5G,OAAO,EAAE;IACd,MAAMoB,MAAM,GAAGpC,aAAa,CAAC6H,WAAW,CAAC7G,OAAO,EAAE,GAAGA,OAAO,CAAC4F,KAAK,CAACkB,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACnG,IAAI1F,MAAM,CAAC2F,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAG/H,mBAAmB,CAACoC,WAAW,CAAC,aAAa,CAAC;MAChE,IAAI2F,SAAS,CAAC1F,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCyF,SAAS,CAAC1F,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/CxC,mBAAmB,CAACyC,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAMwF,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACjH,OAAO,EAAE;IACd,MAAMoB,MAAM,GAAGpC,aAAa,CAACkI,YAAY,CAAClH,OAAO,EAAE,GAAGA,OAAO,CAAC4F,KAAK,CAACkB,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;IACpG,IAAI1F,MAAM,CAAC2F,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAG/H,mBAAmB,CAACoC,WAAW,CAAC,aAAa,CAAC;MAChE,IAAI2F,SAAS,CAAC1F,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCyF,SAAS,CAAC1F,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/CxC,mBAAmB,CAACyC,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,MAAM0F,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAACnH,OAAO,EAAE;IACd,MAAMoB,MAAM,GAAG,MAAMpC,aAAa,CAACoI,eAAe,CAACpH,OAAO,CAACwE,OAAO,CAAC;IACnExF,aAAa,CAACqI,WAAW,CAACjG,MAAM,CAAC6D,OAAO,EAAE7D,MAAM,CAAC2F,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;IAC/E,IAAI3F,MAAM,CAAC2F,OAAO,EAAE;MAClB;MACA,MAAMC,SAAS,GAAG/H,mBAAmB,CAACoC,WAAW,CAAC,aAAa,CAAC;MAChE,IAAI2F,SAAS,CAAC1F,eAAe,CAACC,MAAM,GAAG,CAAC,EAAE;QACxCyF,SAAS,CAAC1F,eAAe,CAACE,OAAO,CAACC,WAAW,IAAI;UAC/CxC,mBAAmB,CAACyC,2BAA2B,CAACD,WAAW,CAAC;QAC9D,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA,MAAM6F,MAAM,GAAGA,CAAA,KAAM;IACnB,IAAI9H,WAAW,KAAK,SAAS,EAAE;MAC7BC,cAAc,CAAC,MAAM,CAAC;MACtBQ,UAAU,CAAC,IAAI,CAAC;IAClB,CAAC,MAAM,IAAIT,WAAW,KAAK,MAAM,EAAE;MACjCC,cAAc,CAAC,OAAO,CAAC;MACvBI,OAAO,CAAC,IAAI,CAAC;MACbE,iBAAiB,CAAC,IAAI,CAAC;IACzB;EACF,CAAC;EAED,MAAMwH,MAAM,GAAGA,CAAA,KAAM;IACnB9H,cAAc,CAAC,OAAO,CAAC;IACvBI,OAAO,CAAC,IAAI,CAAC;IACbE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,UAAU,CAAC,IAAI,CAAC;IAChBN,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;;EAED;EACA,MAAM6H,UAAU,GAAGA,CAAA,KAAM;IACvB1G,YAAY,CAAC2G,OAAO,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAC9ClH,OAAO,CAAC;MAAEW,EAAE,EAAE,OAAO;MAAEP,IAAI,EAAE,WAAW;MAAEQ,gBAAgB,EAAE;IAAU,CAAC,CAAC;EAC1E,CAAC;EAED,oBACEhC,OAAA;IAAKuI,SAAS,EAAC,KAAK;IAACC,GAAG,EAAEnH,MAAO;IAAAoH,QAAA,gBAE/BzI,OAAA;MAAQuI,SAAS,EAAC,YAAY;MAAAE,QAAA,eAC5BzI,OAAA;QAAKuI,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BzI,OAAA;UAAQ0I,OAAO,EAAEN,MAAO;UAACG,SAAS,EAAC,WAAW;UAAAE,QAAA,EAAC;QAE/C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9I,OAAA;UAAKuI,SAAS,EAAC,cAAc;UAAAE,QAAA,GAC1BtH,IAAI,iBACHnB,OAAA;YAAK+B,EAAE,EAAC,wBAAwB;YAACgH,KAAK,EAAE;cAAEC,WAAW,EAAE;YAAO;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE3D,CACN,EACA,CAAC3H,IAAI,gBACJnB,OAAA;YAAQ0I,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAAC;UAEzD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,gBAET9I,OAAA;YAAAyI,QAAA,GAAM,WAAS,EAACtH,IAAI,CAACK,IAAI,EAAC,GAAC;UAAA;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAClC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGT9I,OAAA;MAAMuI,SAAS,EAAC,cAAc;MAAAE,QAAA,GAC3BxH,KAAK,iBACJjB,OAAA;QAAKuI,SAAS,EAAC,OAAO;QAAAE,QAAA,GAAC,eAClB,EAACxH,KAAK,eACTjB,OAAA;UAAQ0I,OAAO,EAAEA,CAAA,KAAMxH,QAAQ,CAAC,IAAI,CAAE;UAAC6H,KAAK,EAAE;YAACE,UAAU,EAAE,MAAM;YAAEC,UAAU,EAAE,MAAM;YAAEC,MAAM,EAAE,MAAM;YAAEC,KAAK,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAS,CAAE;UAAAZ,QAAA,EAAC;QAE3I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAGAzI,WAAW,KAAK,OAAO,iBACtBL,OAAA;QAAKuI,SAAS,EAAC,kBAAkB;QAAAE,QAAA,gBAC/BzI,OAAA;UAAIuI,SAAS,EAAC,OAAO;UAAAE,QAAA,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClD9I,OAAA;UAAGuI,SAAS,EAAC,UAAU;UAAAE,QAAA,EAAC;QAExB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAEH,CAAC3H,IAAI,gBACJnB,OAAA;UAAK+I,KAAK,EAAE;YAACG,UAAU,EAAE,SAAS;YAAEI,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAf,QAAA,gBACjGzI,OAAA;YAAG+I,KAAK,EAAE;cAACK,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE;YAAM,CAAE;YAAAf,QAAA,EAAC;UAEpD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ9I,OAAA;YAAQ0I,OAAO,EAAEL,UAAW;YAACE,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAAC;UAEzD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAEN9I,OAAA;UAAMyJ,QAAQ,EAAE/C,YAAa;UAAA+B,QAAA,gBAC3BzI,OAAA;YAAKuI,SAAS,EAAC,YAAY;YAAAE,QAAA,eACzBzI,OAAA;cACE0J,IAAI,EAAC,MAAM;cACXC,KAAK,EAAEpJ,KAAM;cACbqJ,QAAQ,EAAGjD,CAAC,IAAKnG,QAAQ,CAACmG,CAAC,CAACkD,MAAM,CAACF,KAAK,CAAE;cAC1CG,WAAW,EAAC,4DAA4D;cACxEvB,SAAS,EAAC,YAAY;cACtBwB,QAAQ,EAAEhJ;YAAU;cAAA4H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN9I,OAAA;YACE0J,IAAI,EAAC,QAAQ;YACbK,QAAQ,EAAEhJ,SAAS,IAAI,CAACR,KAAK,CAACsG,IAAI,CAAC,CAAE;YACrC0B,SAAS,EAAC,iBAAiB;YAAAE,QAAA,EAE1B1H,SAAS,gBACRf,OAAA,CAAAE,SAAA;cAAAuI,QAAA,gBACEzI,OAAA;gBAAMuI,SAAS,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,iBAEnC;YAAA,eAAE,CAAC,gBAEH9I,OAAA,CAAAE,SAAA;cAAAuI,QAAA,EAAE;YAEF,gBAAE;UACH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGAzI,WAAW,KAAK,MAAM,IAAII,IAAI,iBAC7BT,OAAA;QAAKuI,SAAS,EAAC,gBAAgB;QAAAE,QAAA,gBAC7BzI,OAAA;UAAKuI,SAAS,EAAC,aAAa;UAAAE,QAAA,gBAC1BzI,OAAA;YAAAyI,QAAA,EAAKhI,IAAI,CAAC6F;UAAI;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpB9I,OAAA;YAAAyI,QAAA,EAAG;UAAoC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC3C9I,OAAA;YAAQ0I,OAAO,EAAEP,MAAO;YAACI,SAAS,EAAC,mBAAmB;YAACQ,KAAK,EAAE;cAACiB,SAAS,EAAE;YAAM,CAAE;YAAAvB,QAAA,EAAC;UAEnF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL/H,SAAS,gBACRf,OAAA;UAAKuI,SAAS,EAAC,SAAS;UAAAE,QAAA,gBACtBzI,OAAA;YAAMuI,SAAS,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjC9I,OAAA;YAAAyI,QAAA,EAAM;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,gBAEN9I,OAAA;UAAKuI,SAAS,EAAC,eAAe;UAAAE,QAAA,EAC3BhI,IAAI,CAACmD,MAAM,CAACqG,GAAG,CAAC,CAACtG,MAAM,EAAEE,KAAK,kBAC7B7D,OAAA;YAEEuI,SAAS,EAAE,eAAe5H,cAAc,KAAKgD,MAAM,GAAG,UAAU,GAAG,EAAE,EAAG;YACxE,cAAYE,KAAM;YAClB,aAAWF,MAAM,CAAC0C,IAAK;YACvB,oBAAkB1C,MAAM,CAACuG,SAAU;YACnCxB,OAAO,EAAEA,CAAA,KAAM5B,kBAAkB,CAACnD,MAAM,CAAE;YAAA8E,QAAA,gBAE1CzI,OAAA;cAAKuI,SAAS,EAAC,cAAc;cAAAE,QAAA,EAAE9E,MAAM,CAACwG;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClD9I,OAAA;cAAIuI,SAAS,EAAC,aAAa;cAAAE,QAAA,EAAE9E,MAAM,CAAC0C;YAAI;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9C9I,OAAA;cAAGuI,SAAS,EAAC,oBAAoB;cAAAE,QAAA,EAAE9E,MAAM,CAACuG;YAAS;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACvDnF,MAAM,CAACyG,YAAY,iBAClBpK,OAAA;cAAK+I,KAAK,EAAE;gBAACsB,QAAQ,EAAE,UAAU;gBAAEjB,KAAK,EAAE,SAAS;gBAAEY,SAAS,EAAE;cAAQ,CAAE;cAAAvB,QAAA,GAAC,UACjE,EAAC9E,MAAM,CAACyG,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC/D,IAAI,CAAC,IAAI,CAAC,EAClD5C,MAAM,CAACyG,YAAY,CAAChI,MAAM,GAAG,CAAC,IAAI,KAAK;YAAA;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CACN,eACD9I,OAAA;cAAKuI,SAAS,EAAC,cAAc;cAACQ,KAAK,EAAE;gBACnCsB,QAAQ,EAAE,SAAS;gBACnBjB,KAAK,EAAE,SAAS;gBAChBY,SAAS,EAAE,QAAQ;gBACnBO,SAAS,EAAE;cACb,CAAE;cAAA9B,QAAA,EAAC;YAEH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA,GAvBDjF,KAAK;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBP,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGAzI,WAAW,KAAK,SAAS,IAAIQ,OAAO,iBACnCb,OAAA;QAAKuI,SAAS,EAAC,gBAAgB;QAAAE,QAAA,eAC7BzI,OAAA;UAAKuI,SAAS,EAAC,MAAM;UAAAE,QAAA,gBACnBzI,OAAA;YAAKuI,SAAS,EAAC,gBAAgB;YAACQ,KAAK,EAAE;cAACS,YAAY,EAAE;YAAM,CAAE;YAAAf,QAAA,gBAC5DzI,OAAA;cAAQ0I,OAAO,EAAEP,MAAO;cAACI,SAAS,EAAC,mBAAmB;cAAAE,QAAA,EAAC;YAEvD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAGT9I,OAAA;cAAKuI,SAAS,EAAC,kBAAkB;cAACQ,KAAK,EAAE;gBACvCyB,OAAO,EAAE,MAAM;gBACfC,GAAG,EAAE,KAAK;gBACVT,SAAS,EAAE,MAAM;gBACjBU,QAAQ,EAAE;cACZ,CAAE;cAAAjC,QAAA,gBAEAzI,OAAA;gBAAKuI,SAAS,EAAC,yBAAyB;gBAACQ,KAAK,EAAE;kBAC9CyB,OAAO,EAAE,MAAM;kBACfG,UAAU,EAAE,QAAQ;kBACpBF,GAAG,EAAE,KAAK;kBACVnB,OAAO,EAAE,UAAU;kBACnBJ,UAAU,EAAE,SAAS;kBACrBK,YAAY,EAAE,KAAK;kBACnBJ,MAAM,EAAE;gBACV,CAAE;gBAAAV,QAAA,gBACAzI,OAAA;kBACE0I,OAAO,EAAE3B,kBAAmB;kBAC5BwB,SAAS,EAAC,UAAU;kBACpB9B,KAAK,EAAC,mBAAmB;kBACzBsC,KAAK,EAAE;oBACLG,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdkB,QAAQ,EAAE,MAAM;oBAChBhB,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAb,QAAA,EAED7I,aAAa,CAACoH,SAAS,CAAC,CAAC,CAACC,SAAS,GAAG,IAAI,GAAG;gBAAI;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,eACT9I,OAAA;kBACE0I,OAAO,EAAEtB,gBAAiB;kBAC1BmB,SAAS,EAAC,UAAU;kBACpB9B,KAAK,EAAC,aAAa;kBACnBsC,KAAK,EAAE;oBACLG,UAAU,EAAE,MAAM;oBAClBC,MAAM,EAAE,MAAM;oBACdkB,QAAQ,EAAE,MAAM;oBAChBhB,MAAM,EAAE,SAAS;oBACjBC,OAAO,EAAE;kBACX,CAAE;kBAAAb,QAAA,EACH;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9I,OAAA;kBACE0J,IAAI,EAAC,OAAO;kBACZkB,GAAG,EAAC,KAAK;kBACTC,GAAG,EAAC,GAAG;kBACPC,IAAI,EAAC,KAAK;kBACVC,YAAY,EAAC,GAAG;kBAChBnB,QAAQ,EAAGjD,CAAC,IAAKW,sBAAsB,CAAC0D,UAAU,CAACrE,CAAC,CAACkD,MAAM,CAACF,KAAK,CAAC,CAAE;kBACpEZ,KAAK,EAAE;oBAACkC,KAAK,EAAE;kBAAM,CAAE;kBACvBxE,KAAK,EAAC;gBAAc;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACF9I,OAAA;kBAAM+I,KAAK,EAAE;oBAACsB,QAAQ,EAAE,MAAM;oBAAEjB,KAAK,EAAE;kBAAS,CAAE;kBAAAX,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eAGN9I,OAAA;gBAAKuI,SAAS,EAAC,yBAAyB;gBAACQ,KAAK,EAAE;kBAC9CyB,OAAO,EAAE,MAAM;kBACfC,GAAG,EAAE;gBACP,CAAE;gBAAAhC,QAAA,gBACAzI,OAAA;kBACE0I,OAAO,EAAEV,qBAAsB;kBAC/BO,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACO,OAAO,EAAE,UAAU;oBAAEe,QAAQ,EAAE;kBAAM,CAAE;kBAC/C5D,KAAK,EAAC,mBAAmB;kBAAAgC,QAAA,EAC1B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9I,OAAA;kBACE0I,OAAO,EAAEjB,eAAgB;kBACzBc,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACO,OAAO,EAAE,UAAU;oBAAEe,QAAQ,EAAE;kBAAM,CAAE;kBAC/C5D,KAAK,EAAC,eAAe;kBAAAgC,QAAA,EACtB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT9I,OAAA;kBACE0I,OAAO,EAAEZ,gBAAiB;kBAC1BS,SAAS,EAAC,mBAAmB;kBAC7BQ,KAAK,EAAE;oBAACO,OAAO,EAAE,UAAU;oBAAEe,QAAQ,EAAE;kBAAM,CAAE;kBAC/C5D,KAAK,EAAC,gBAAgB;kBAAAgC,QAAA,EACvB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN9I,OAAA;YAAIuI,SAAS,EAAC,OAAO;YAAAE,QAAA,EAAE5H,OAAO,CAAC4F;UAAK;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1C9I,OAAA;YAAK+I,KAAK,EAAE;cAACK,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE,MAAM;cAAEa,QAAQ,EAAE;YAAQ,CAAE;YAAA5B,QAAA,gBACvEzI,OAAA;cAAAyI,QAAA,GAAM,WAAS,EAAC5H,OAAO,CAACN,KAAK;YAAA;cAAAoI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACpCjI,OAAO,CAACuF,KAAK,IAAIvF,OAAO,CAACuF,KAAK,CAAChE,MAAM,GAAG,CAAC,iBACxCpC,OAAA;cAAM+I,KAAK,EAAE;gBAACE,UAAU,EAAE;cAAM,CAAE;cAAAR,QAAA,GAAC,SAC1B,EAAC5H,OAAO,CAACuF,KAAK,CAACG,IAAI,CAAC,IAAI,CAAC;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN9I,OAAA;YAAKuI,SAAS,EAAC,iBAAiB;YAACQ,KAAK,EAAE;cAACmC,UAAU,EAAE,KAAK;cAAEb,QAAQ,EAAE;YAAQ,CAAE;YAAA5B,QAAA,EAC7E5H,OAAO,CAACwE,OAAO,CAAC8F,KAAK,CAAC,IAAI,CAAC,CAAClB,GAAG,CAAC,CAACmB,SAAS,EAAEvH,KAAK,KAChDuH,SAAS,CAACvE,IAAI,CAAC,CAAC,iBACd7G,OAAA;cAAe+I,KAAK,EAAE;gBAACS,YAAY,EAAE;cAAM,CAAE;cAAAf,QAAA,EAC1C2C;YAAS,GADJvH,KAAK;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CAEN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC1I,EAAA,CA9mBID,YAAY;AAAAkL,EAAA,GAAZlL,YAAY;AAgnBlB,eAAeA,YAAY;AAAC,IAAAkL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}